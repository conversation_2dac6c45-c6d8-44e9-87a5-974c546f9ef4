# Google Authentication Setup Guide

## 🚀 Overview

This guide will help you set up Google authentication for the Wasel app. Users will be able to sign in using their Google accounts with a "Continue with Google" button.

## 📋 Prerequisites

1. Google Cloud Console account
2. Wasel app project set up
3. Domain or app scheme configured

## 🔧 Google Cloud Console Setup

### Step 1: Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Name it "Wasel App" or similar

### Step 2: Enable Google+ API

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Google+ API" and enable it
3. Also enable "Google OAuth2 API"

### Step 3: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Configure the consent screen first if prompted

#### For Web Application:
- Application type: Web application
- Name: Wasel Web App
- Authorized redirect URIs: 
  - `http://localhost:19006` (for development)
  - `https://yourdomain.com` (for production)

#### For Android:
- Application type: Android
- Name: Wasel Android App
- Package name: `com.wasel.app` (or your actual package name)
- SHA-1 certificate fingerprint: (get from your keystore)

#### For iOS:
- Application type: iOS
- Name: Wasel iOS App
- Bundle ID: `com.wasel.app` (or your actual bundle ID)

## 🔑 Configuration

### Step 4: Update Google Auth Service

Edit `services/googleAuthService.ts` and replace the placeholder client IDs:

```typescript
const GOOGLE_CLIENT_ID = {
  ios: 'YOUR_IOS_CLIENT_ID.apps.googleusercontent.com',
  android: 'YOUR_ANDROID_CLIENT_ID.apps.googleusercontent.com',
  web: 'YOUR_WEB_CLIENT_ID.apps.googleusercontent.com',
};
```

### Step 5: Update App Configuration

#### For Expo (app.json/app.config.js):
```json
{
  "expo": {
    "scheme": "com.wasel.app",
    "android": {
      "googleServicesFile": "./google-services.json"
    },
    "ios": {
      "googleServicesFile": "./GoogleService-Info.plist"
    }
  }
}
```

#### For React Native (android/app/build.gradle):
```gradle
dependencies {
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
}
```

## 📱 Platform-Specific Setup

### Android Setup

1. Download `google-services.json` from Firebase Console
2. Place it in your `android/app/` directory
3. Add to `android/app/build.gradle`:
```gradle
apply plugin: 'com.google.gms.google-services'
```

### iOS Setup

1. Download `GoogleService-Info.plist` from Firebase Console
2. Add it to your iOS project in Xcode
3. Add URL scheme to `ios/YourApp/Info.plist`:
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>com.wasel.app</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>com.wasel.app</string>
        </array>
    </dict>
</array>
```

## 🧪 Testing

### Development Testing

1. Start your development server:
```bash
npm start
```

2. Test on different platforms:
- Web: Browser should open Google OAuth popup
- Mobile: Should redirect to Google sign-in page
- Simulator: Use test Google account

### Production Testing

1. Build and deploy your app
2. Test with real Google accounts
3. Verify user data is correctly saved to backend

## 🔒 Security Considerations

1. **Client ID Security**: Keep client IDs in environment variables for production
2. **Token Validation**: Backend validates Google tokens before creating sessions
3. **User Data**: Only necessary user data is stored
4. **HTTPS**: Always use HTTPS in production

## 🐛 Troubleshooting

### Common Issues

1. **"Invalid Client ID"**: Check that client ID matches platform
2. **"Redirect URI Mismatch"**: Ensure redirect URIs are configured correctly
3. **"App Not Verified"**: Submit app for Google verification if needed
4. **Network Errors**: Check internet connection and API endpoints

### Debug Steps

1. Check console logs for detailed error messages
2. Verify Google Cloud Console configuration
3. Test with different Google accounts
4. Check network requests in browser dev tools

## 📚 Resources

- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Expo AuthSession Documentation](https://docs.expo.dev/versions/latest/sdk/auth-session/)
- [React Native Google Sign-In](https://github.com/react-native-google-signin/google-signin)

## ✅ Checklist

- [ ] Google Cloud project created
- [ ] OAuth 2.0 credentials configured
- [ ] Client IDs updated in code
- [ ] Platform-specific setup completed
- [ ] App scheme configured
- [ ] Google services files added
- [ ] Testing completed
- [ ] Production deployment verified

## 🎉 Ready to Use!

Once configured, users can:
1. Click "Continue with Google" on login page
2. Sign in with their Google account
3. Be automatically registered/logged in to Wasel
4. Access all app features immediately

The Google authentication is now fully integrated with your Wasel app!
