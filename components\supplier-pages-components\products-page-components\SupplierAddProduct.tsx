import { Ionicons } from "@expo/vector-icons"
import { Dimensions, Image } from "react-native"
import { Button, Card, Input, Label, ScrollView, Text, YStack, Select, View } from "tamagui"
import * as ImagePicker from "expo-image-picker"
import { useRouter } from "expo-router"
import { useSupplierProducts } from "./useSupplierProducts"
import uuid from 'react-native-uuid';
import { useSupplierCategories } from "./useSupplierCategories"
import { useEffect, useState } from "react";
import { AnimatePresence, MotiView } from "moti"

export default function AddProductScreen() {
  const { addProduct } = useSupplierProducts();
  const router = useRouter();
  const width = Dimensions.get("window").width;

  const [product, setProduct] = useState({
    id: uuid.v4(),
    name: "",
    price: 0,
    image: "",
    category: "",
    restaurantOptions: {
      additions: [],
      without: [],
      sides: [],
    },
  });

  const [error, setError] = useState("");

  const handleImagePick = async () => {
    const res = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      quality: 1,
    })

    if (!res.canceled && res.assets[0]?.uri) {
      setProduct({ ...product, image: res.assets[0].uri })
    }
  };

  const validateAndContinue = () => {
    if (!product.name || !product.price || !product.category || !product.image) {
      setError("All fields are required.")
      return
    }
    setError("")

    // Add product to global list
    addProduct(product)

    // Navigate to additions screen with ID
    router.push({
      pathname: "/(supplier-pages)/products/manage-additions",
      params: { id: product.id },
    })
  };

  const { categories } = useSupplierCategories();

  const filteredCategories = categories.filter((cat) => cat !== "All");

  // Set default category to first available (excluding 'All')
  useEffect(() => {
    if (!product.category && filteredCategories.length > 0) {
      setProduct((prev) => ({ ...prev, category: filteredCategories[0] }));
    }
    // eslint-disable-next-line
  }, [filteredCategories.length]);

  const [selectOpen, setSelectOpen] = useState(false);

  return (
    <ScrollView padding={16} contentContainerStyle={{paddingBottom: 120}} width={width}>
      <MotiView
        from={{ opacity: 0, translateY: -20 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ duration: 500 }}
        style={{
          backgroundColor: "#7529B3",
          padding: 20,
          borderRadius: 20,
          marginBottom: 24,
        }}
      >
        <Text color="white" fontSize="$8" fontWeight="bold">
          Add New Product
        </Text>
        <Text color="white" mt="$1">
          Fill the product details below
        </Text>
      </MotiView>

      <Card gap="$4" p="$4" elevate br="$6" bg="#FAFAFF">
        <YStack gap="$3">

            {/* Product Name */}
            <Label>
            <Text color="#7529B3" fontWeight="600">
                <Ionicons name="pricetag-outline" size={16} /> Product Name
            </Text>
            </Label>
            <Input
            size="$4"
            placeholder="e.g. Chicken Burger"
            value={product.name}
            onChangeText={(text) => setProduct({ ...product, name: text })}
            backgroundColor="#F5F3FF"
            borderColor="#ddd"
            borderWidth={1}
            borderRadius={10}
            padding={12}
            />

            {/* Category dropdown */}
            <Label>
                <Text color="#7529B3" fontWeight="600">
                    <Ionicons name="list-outline" size={16} /> Category
                </Text>
            </Label>
            <Select
                open={selectOpen}
                onOpenChange={setSelectOpen}
                value={product.category}
                onValueChange={(value) => setProduct({ ...product, category: value })}
                >
                <Select.Trigger
                    iconAfter={<Ionicons name="chevron-down" size={16} color="#7529B3" />}
                    onPress={() => setSelectOpen((v) => !v)}
                    style={{
                    borderWidth: 1,
                    borderColor: "#ccc",
                    borderRadius: 10,
                    paddingVertical: 14,
                    paddingHorizontal: 16,
                    backgroundColor: "#F8F7FC",
                    }}
                >
                    <Text
                    fontSize={"$4"}
                    color={product.category ? "#7529B3" : "#999"}
                    >
                    {product.category || "Select a category"}
                    </Text>
                </Select.Trigger>

                <AnimatePresence>
                    {selectOpen && (
                    <Select.Content zIndex={100}>
                        <MotiView
                        from={{ opacity: 0, translateY: -10, scale: 0.97 }}
                        animate={{ opacity: 1, translateY: 0, scale: 1 }}
                        exit={{ opacity: 0, translateY: -10, scale: 0.97 }}
                        transition={{ type: "timing", duration: 250 }}
                        style={{
                            backgroundColor: "white",
                            borderRadius: 14,
                            paddingVertical: 6,
                            paddingHorizontal: 6,
                            shadowColor: "#000",
                            shadowOpacity: 0.08,
                            shadowRadius: 12,
                            shadowOffset: { width: 0, height: 6 },
                            elevation: 8,
                        }}
                        >
                        {filteredCategories.map((cat, idx) => {
                            const selected = product.category === cat
                            return (
                            <Select.Item
                                key={cat}
                                value={cat}
                                index={idx}
                                style={{
                                backgroundColor: selected ? "#7529B3" : "#fff",
                                paddingVertical: 12,
                                paddingHorizontal: 18,
                                borderRadius: 8,
                                marginBottom: 4,
                                }}
                            >
                                <Select.ItemText
                                style={{
                                    fontSize: 15,
                                    color: selected ? "#fff" : "#333",
                                    fontWeight: selected ? "bold" : "normal",
                                }}
                                >
                                {cat}
                                </Select.ItemText>
                                {selected && (
                                <View style={{ position: "absolute", right: 14 }}>
                                    <Ionicons name="checkmark" size={16} color="#fff" />
                                </View>
                                )}
                            </Select.Item>
                            )
                        })}
                        </MotiView>
                    </Select.Content>
                    )}
                </AnimatePresence>
            </Select>

            {/* Price */}
            <Label>
            <Text color="#7529B3" fontWeight="600">
                <Ionicons name="cash-outline" size={16} /> Price (₪)
            </Text>
            </Label>
            <Input
            size="$4"
            keyboardType="numeric"
            placeholder="e.g. 25"
            value={product.price?.toString()}
            onChangeText={(text) =>
                setProduct({ ...product, price: Number(text) })
            }
            backgroundColor="#F5F3FF"
            borderColor="#ddd"
            borderWidth={1}
            borderRadius={10}
            padding={12}
            />

            {/* Image Upload */}
            <Label>
            <Text color="#7529B3" fontWeight="600">
                <Ionicons name="image-outline" size={16} /> Product Image
            </Text>
            </Label>
            <Button
            bg="#F5F3FF"
            borderColor="#ccc"
            borderWidth={1}
            borderRadius={10}
            pressStyle={{ bg: "#E4DEFF" }}
            onPress={handleImagePick}
            icon={<Ionicons name="image" size={20} color="#7529B3" />}
            >
            <Text color="#7529B3">
                {product.image ? "Change Image" : "Upload Image"}
            </Text>
            </Button>

            {product.image && (
            <Image
                source={{ uri: product.image }}
                style={{
                marginTop: 10,
                width: width - 80,
                height: 180,
                borderRadius: 12,
                alignSelf: "center",
                }}
                resizeMode="cover"
            />
            )}

            {/* Error Message */}
            {error && (
            <Text color="$red10" fontWeight="600" mt="$2">
                <Ionicons name="warning-outline" size={14} /> {error}
            </Text>
            )}
        </YStack>
        </Card>

      <Button
        mt="$6"
        theme="active"
        br="$10"
        icon={<Ionicons name="arrow-forward-outline" size={22} />}
        onPress={validateAndContinue}
        bg="$secondary"
        color="white"
        hoverStyle={{ bg: "$secondary_strong" }} 
        pressStyle={{ bg: "$secondary_strong" }}
      >
        <Text color="white">Continue to Add Options</Text>
      </Button>
    </ScrollView>
  )
}
