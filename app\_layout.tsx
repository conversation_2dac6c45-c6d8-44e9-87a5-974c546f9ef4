import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import { useEffect, useState } from 'react';
import { TamaguiProvider } from 'tamagui';
import { I18nextProvider } from 'react-i18next';

import config from '../tamagui.config';
import i18n from '../i18n';
import { useLanguageStore } from '../stores/languageStore';
import { ErrorBoundary } from '../components/common/ErrorBoundary';

export default function Layout() {
  const [loaded] = useFonts({
    Inter: require('@tamagui/font-inter/otf/Inter-Medium.otf'),
    InterBold: require('@tamagui/font-inter/otf/Inter-Bold.otf'),
  });

  const [appReady, setAppReady] = useState(false);
  const { initializeLanguage } = useLanguageStore();

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
      // Initialize language store and mark app as ready
      initializeLanguage().finally(() => {
        setAppReady(true);
      });
    }
  }, [loaded, initializeLanguage]);

  // Don't render anything until fonts are loaded and app is ready
  if (!loaded || !appReady) {
    return null;
  }

  return (
    <ErrorBoundary>
      <TamaguiProvider config={config}>
        <I18nextProvider i18n={i18n}>
          <Stack screenOptions={{headerShown: false}}/>
        </I18nextProvider>
      </TamaguiProvider>
    </ErrorBoundary>
  );
}
