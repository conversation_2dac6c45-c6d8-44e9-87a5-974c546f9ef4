# Google OAuth Setup Guide

## Current Status
The Google authentication is currently in **demo mode** to prevent OAuth errors. Follow this guide to set up proper Google OAuth credentials.

## Steps to Configure Google OAuth

### 1. Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API and Google OAuth2 API

### 2. Configure OAuth Consent Screen
1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type
3. Fill in the required information:
   - App name: "<PERSON><PERSON>"
   - User support email: Your email
   - Developer contact information: Your email
4. Add scopes: `email`, `profile`, `openid`
5. Add test users if needed

### 3. Create OAuth 2.0 Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Create credentials for each platform:

#### For Android:
- Application type: Android
- Package name: `com.wasel.app` (or your actual package name)
- SHA-1 certificate fingerprint: Get from your keystore

#### For iOS:
- Application type: iOS
- Bundle ID: `com.wasel.app` (or your actual bundle ID)

#### For Web (Development):
- Application type: Web application
- Authorized redirect URIs: Add Expo development URLs

### 4. Update Configuration
1. Replace the client IDs in `services/googleAuthService.ts`:
```typescript
const GOOGLE_CLIENT_ID = {
  ios: 'YOUR_ACTUAL_IOS_CLIENT_ID.apps.googleusercontent.com',
  android: 'YOUR_ACTUAL_ANDROID_CLIENT_ID.apps.googleusercontent.com',
  web: 'YOUR_ACTUAL_WEB_CLIENT_ID.apps.googleusercontent.com',
};
```

2. Uncomment the actual OAuth implementation in the `signInWithGoogle` method
3. Comment out or remove the demo implementation

### 5. Test the Implementation
1. Build and test on actual devices
2. Ensure all redirect URIs are properly configured
3. Test with different Google accounts

## Common Issues and Solutions

### "Access blocked: Authorization Error"
- Ensure OAuth consent screen is properly configured
- Add your email as a test user
- Verify client IDs are correct for each platform

### "invalid_request" Error
- Check redirect URIs match exactly
- Ensure package name/bundle ID matches your app configuration
- Verify SHA-1 fingerprint is correct for Android

### Development vs Production
- Use different client IDs for development and production
- Configure appropriate redirect URIs for each environment
- Test thoroughly before publishing

## Current Demo Mode
The app currently uses demo Google authentication to prevent OAuth errors. This allows you to:
- Test the UI flow
- Develop other features
- Avoid OAuth configuration issues during development

To enable real Google authentication, follow the steps above and update the service configuration.
