import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import { Platform } from 'react-native';

// Complete the auth session for web
WebBrowser.maybeCompleteAuthSession();

// Google OAuth configuration
const GOOGLE_CLIENT_ID = {
  // TODO: Add your iOS Client ID here
  ios: 'YOUR_IOS_CLIENT_ID.apps.googleusercontent.com',
  // Android Client ID (configured)
  android: '474217145445-v9t85si9h1uqda8als9rrr9rfeov4dhl.apps.googleusercontent.com',
  // TODO: Add your Web Client ID here
  web: 'YOUR_WEB_CLIENT_ID.apps.googleusercontent.com',
};

export interface GoogleUser {
  id: string;
  email: string;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  verified_email: boolean;
}

export interface GoogleAuthResult {
  success: boolean;
  user?: GoogleUser;
  error?: string;
  accessToken?: string;
}

class GoogleAuthService {
  private getClientId(): string {
    if (Platform.OS === 'ios') {
      return GOOGLE_CLIENT_ID.ios;
    } else if (Platform.OS === 'android') {
      return GOOGLE_CLIENT_ID.android;
    } else {
      return GOOGLE_CLIENT_ID.web;
    }
  }

  async signInWithGoogle(): Promise<GoogleAuthResult> {
    try {
      // For now, return a demo/development response
      // This will be replaced with actual Google OAuth once credentials are configured

      console.log('Google Sign-In attempted - Demo mode');

      // Simulate Google authentication for development
      const demoUser: GoogleUser = {
        id: 'demo_google_user_' + Date.now(),
        email: '<EMAIL>',
        name: 'Demo User',
        given_name: 'Demo',
        family_name: 'User',
        picture: 'https://via.placeholder.com/150',
        verified_email: true,
      };

      // Simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        user: demoUser,
        accessToken: 'demo_access_token_' + Date.now(),
      };

      /*
      // Uncomment this section once you have proper Google OAuth credentials configured

      const clientId = this.getClientId();

      // Create the auth request
      const request = new AuthSession.AuthRequest({
        clientId,
        scopes: ['openid', 'profile', 'email'],
        responseType: AuthSession.ResponseType.Code,
        redirectUri: AuthSession.makeRedirectUri({
          useProxy: true,
        }),
        additionalParameters: {},
        extraParams: {
          access_type: 'offline',
        },
      });

      // Perform the authentication
      const result = await request.promptAsync({
        authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
        useProxy: true,
        showInRecents: true,
      });

      if (result.type === 'success') {
        // Exchange the authorization code for an access token
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId,
            code: result.params.code,
            redirectUri: AuthSession.makeRedirectUri({
              useProxy: true,
            }),
            extraParams: {
              code_verifier: request.codeVerifier || '',
            },
          },
          {
            tokenEndpoint: 'https://oauth2.googleapis.com/token',
          }
        );

        if (tokenResult.accessToken) {
          // Get user info from Google
          const userInfo = await this.getUserInfo(tokenResult.accessToken);

          return {
            success: true,
            user: userInfo,
            accessToken: tokenResult.accessToken,
          };
        } else {
          return {
            success: false,
            error: 'Failed to get access token',
          };
        }
      } else if (result.type === 'cancel') {
        return {
          success: false,
          error: 'User cancelled the authentication',
        };
      } else {
        return {
          success: false,
          error: 'Authentication failed',
        };
      }
      */
    } catch (error) {
      console.error('Google Sign-In Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  private async getUserInfo(accessToken: string): Promise<GoogleUser> {
    const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch user info');
    }

    return await response.json();
  }

  async signOut(): Promise<void> {
    try {
      // For web, we can revoke the token
      // For mobile, the session is handled by the browser
      console.log('Google sign out completed');
    } catch (error) {
      console.error('Google Sign-Out Error:', error);
    }
  }
}

export const googleAuthService = new GoogleAuthService();
