import { ScrollView } from 'react-native';
import { Text, View, YStack, XStack, Button, Separator, H4, H6, H5, <PERSON> } from 'tamagui';
import { useRouter } from 'expo-router';
import { useMyOrdersStore, MyOrder } from '../../customer-pages-components/orders-page-components/useMyOrdersStore';
import { Ionicons } from '@expo/vector-icons';
import { useCurrentUserData } from '~/components/useCurrentUserData';
import { Dimensions } from 'react-native';
import { suppliersData } from '~/temp-data/suppliersData';
import { useTranslation } from 'react-i18next';

export default function SupplierHomeGUI() {
  const { t } = useTranslation();
  const router = useRouter();
  const orders = useMyOrdersStore((s) => s.orders);
  const { user } = useCurrentUserData();
  const supplierData = suppliersData.find((supplier) =>
    supplier.id === user?.supplierId
  );

  const newOrders = orders.filter((o) => o.status === 'Pending' && o.supplierId === user?.supplierId);
  const inPreparingOrders = orders.filter((o) => o.status === 'Preparing' && o.supplierId === user?.supplierId);
  const onTheWayOrders = orders.filter((o) => o.status === 'On the Way' && o.supplierId === user?.supplierId);
  const deliveredOrders = orders.filter((o) => o.status === 'Delivered' && o.supplierId === user?.supplierId);

  const windowWidth = Dimensions.get('window').width;

  const renderOrderCard = (order: MyOrder) => (
    <Card width={"100%"} elevate p="$4" br="$6" bg="$gray1" borderColor="$gray4" key={order.id}>
        <YStack jc="space-between" ai="center" mb="$2">
            <H5 color="$color">#{order.id}</H5>
            <Text
            fontSize="$2"
            bg={order.status === 'Preparing' ? "$yellow7" : order.status === 'On the Way' ? "$orange6" : order.status === "Delivered" ? "$green6" : "$red7"}
            px="$2"
            py="$1"
            br="$4"
            color="white"
            >
            {order.status}
            </Text>
        </YStack>

        <YStack gap="$2">
            <XStack ai="center" gap="$2">
            <Ionicons name="cube-outline" size={18} color="#6B7280" />
            <Text>{order.items.length} items</Text>
            </XStack>
            <XStack ai="center" gap="$2">
            <Ionicons name="cash-outline" size={18} color="#6B7280" />
            <Text>${order.total.toFixed(2)}</Text>
            </XStack>
            <XStack ai="center" gap="$2">
            <Ionicons name="location-outline" size={18} color="#6B7280" />
            <Text numberOfLines={2}>{order.address?.address || 'No address'}</Text>
            </XStack>
        </YStack>

        <YStack mt="$3" gap="$2">
            <Button mt="$3" size="$3" br="$6" borderColor={"$primary"} color={"$primary"} variant="outlined" 
              icon={<Ionicons name="eye-outline" size={18} color="#7529B3"/>}
              onPress={() => router.push({
                pathname: "/(supplier-pages)/home/<USER>",
                params: {orderId: order.id}
              })}
            >
            View
            </Button>
            {order.status === 'Pending' && (
            <Button
                mt="$3" size="$3" br="$6" borderColor={"$secondary"} color={"$secondary"} variant="outlined"
                icon={<Ionicons name="checkmark-circle-outline" size={16} color="#67B329" />}
                onPress={() => {
                useMyOrdersStore.setState((state) => ({
                    orders: state.orders.map((o) =>
                    o.id === order.id ? { ...o, status: 'Preparing' } : o
                    ),
                }));
                }}
            >
                Dispatch
            </Button>
            )}
        </YStack>
        </Card>
  );

  const Section = ({
    title,
    icon,
    color,
    orders,
  }: {
    title: string;
    icon: string;
    color: string;
    orders: MyOrder[];
  }) => (
    <YStack gap="$2">
      <XStack ai="center" gap="$2" mb="$1">
        <Ionicons name={icon as any} size={18} color={color} />
        <H6 color={color}>{title}</H6>
      </XStack>
      {orders.length ? (
        orders.map(renderOrderCard)
      ) : (
        <Text color="$gray9">{t('supplier.noOrders', { defaultValue: 'No orders' })}</Text>
      )}
    </YStack>
  );

  return (
    <ScrollView contentContainerStyle={{ padding: 16, paddingBottom: 120, width: windowWidth }}>
      <YStack gap="$5">
        <Card bg="$primary" p="$4" br="$8" mb="$5" elevate>
            <YStack gap="$2">
                <H4 color="white">👋 {t('supplier.hello', { defaultValue: 'Hello' })}, {supplierData?.name}</H4>
                <XStack jc="space-between">
                <Text color="white">{t('supplier.role', { defaultValue: 'Role' })}: {t('supplier.supplier', { defaultValue: 'Supplier' })}</Text>
                <Text color="white">{t('supplier.orders', { defaultValue: 'Orders' })}: {orders.length}</Text>
                </XStack>
            </YStack>
            </Card>

        <Section
          title={t('supplier.newOrders', { defaultValue: 'New Orders' })}
          icon="alert-circle-outline"
          color="#EF4444"
          orders={newOrders}
        />

        <Separator />

        <Section
          title={t('supplier.inPreparingOrders', { defaultValue: 'In Preparing Orders' })}
          icon="timer-outline"
          color="#e4c000"
          orders={inPreparingOrders}
        />

        <Separator />

        <Section
          title={t('supplier.onTheWayOrders', { defaultValue: 'On The Way Orders' })}
          icon="car-outline"
          color="#F97316"
          orders={onTheWayOrders}
        />

        <Separator />

        <Section
          title={t('supplier.deliveredOrders', { defaultValue: 'Delivered Orders' })}
          icon="checkmark-done-outline"
          color="#10B981"
          orders={deliveredOrders}
        />
      </YStack>
    </ScrollView>
  );
}
