import { create } from 'zustand';
import { CartItem } from '../CartStore';

type Location = {
  lat: number;
  lng: number;
  address?: string;
};

export type MyOrder = {
  id: string
  createdAt: string
  items: CartItem[]
  supplierId: string
  total: number
  status: 'Pending' | 'Preparing' | 'On the Way' | 'Delivered'
  supplierRecievedMoney: boolean
  address: Location | null
  phone: string
  note: string
  paymentMethod: 'cash' | 'card'
  promo: string
  subTotal: number
  deliveryFee: number
  estimatedTime: string
  driverName: string
  driverPhone: string
  driverLocation: Location
};

type Store = {
  orders: MyOrder[]
  addOrder: (order: MyOrder) => void
};

export const useMyOrdersStore = create<Store>((set) => ({
  orders: [],
  addOrder: (order) => set((state) => ({
    orders: [order, ...state.orders] // latest first
  }))
}));
