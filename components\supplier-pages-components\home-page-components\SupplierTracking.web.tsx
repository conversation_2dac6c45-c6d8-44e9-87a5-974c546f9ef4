import { useEffect, useRef, useState } from 'react';
import { ScrollView, Dimensions, Pressable, Modal, Linking } from 'react-native';
import { useLocalSearchParams, Stack } from 'expo-router';
import { <PERSON>, YStack, Card, H4, H6, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ner, Separator, Button, View } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { useMyOrdersStore } from '~/components/customer-pages-components/orders-page-components/useMyOrdersStore';
// Use react-native-web-maps directly for web
import MapView, { <PERSON><PERSON>, Polyline } from 'react-native-web-maps';

export default function SupplierTracking() {
  const { orderId } = useLocalSearchParams();
  const { width, height } = Dimensions.get('window');
  
  const orders = useMyOrdersStore((state) => state.orders);
  const order = orders.find(o => o.id === orderId);
  
  const [driverLocation, setDriverLocation] = useState<[number, number]>([35.9106, 31.9539]);
  const [estimatedTime, setEstimatedTime] = useState(25);

  useEffect(() => {
    // Simulate driver movement
    const interval = setInterval(() => {
      setDriverLocation(prev => [
        prev[0] + (Math.random() - 0.5) * 0.001,
        prev[1] + (Math.random() - 0.5) * 0.001
      ]);
      setEstimatedTime(prev => Math.max(1, prev - 1));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const handleCallDriver = () => {
    Linking.openURL('tel:+970599123456');
  };

  if (!order) {
    return (
      <View flex={1} justifyContent="center" alignItems="center">
        <Text>Order not found</Text>
      </View>
    );
  }

  const getMapRegion = () => {
    if (!order.deliveryAddress) {
      return {
        latitude: 32.2211,
        longitude: 35.2544,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
    }

    const delivery = order.deliveryAddress.coordinates;
    return {
      latitude: delivery[1],
      longitude: delivery[0],
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
  };

  const getPolylineCoordinates = () => {
    if (!order.deliveryAddress) return [];
    
    const delivery = order.deliveryAddress.coordinates;
    
    return [
      { latitude: 31.9539, longitude: 35.9106 }, // Restaurant location
      { latitude: driverLocation[1], longitude: driverLocation[0] },
      { latitude: delivery[1], longitude: delivery[0] },
    ];
  };

  return (
    <>
      <Stack.Screen 
        options={{ 
          title: `Order #${order.id.slice(-6)}`,
          headerShown: true 
        }} 
      />
      
      <ScrollView style={{ flex: 1 }}>
        {/* Map Section */}
        <View style={{ height: height * 0.4 }}>
          <MapView
            style={{ width, height: height * 0.4 }}
            region={getMapRegion()}
            showsUserLocation={true}
          >
            {/* Restaurant Marker */}
            <Marker
              coordinate={{
                latitude: 32.2211,
                longitude: 35.2544,
              }}
              pinColor="orange"
              title="Restaurant"
            />
            
            {/* Delivery Marker */}
            {order.deliveryAddress && (
              <Marker
                coordinate={{
                  latitude: order.deliveryAddress.coordinates[1],
                  longitude: order.deliveryAddress.coordinates[0],
                }}
                pinColor="red"
                title="Delivery Location"
              />
            )}
            
            {/* Driver Marker */}
            <Marker
              coordinate={{
                latitude: driverLocation[1],
                longitude: driverLocation[0],
              }}
              pinColor="blue"
              title="Driver Location"
            />
            
            {/* Route Polyline */}
            <Polyline
              coordinates={getPolylineCoordinates()}
              strokeColor="#007AFF"
              strokeWidth={3}
            />
          </MapView>
        </View>

        {/* Order Status */}
        <YStack p="$4" gap="$4">
          <Card p="$4" bg="$blue2" br="$4">
            <YStack gap="$2">
              <H4 color="$blue11">Order in Progress</H4>
              <Text color="$blue10">
                Estimated delivery: {estimatedTime} minutes
              </Text>
            </YStack>
          </Card>

          {/* Driver Info */}
          <Card p="$4" br="$4">
            <YStack gap="$3">
              <H6>Driver Information</H6>
              <XStack gap="$3" alignItems="center">
                <View w={50} h={50} br={25} bg="$gray5" />
                <YStack flex={1}>
                  <Text fontSize="$4" fontWeight="500">Ahmad Hassan</Text>
                  <Text fontSize="$3" color="$gray10">★ 4.8 • Toyota Corolla</Text>
                </YStack>
                <Button
                  size="$3"
                  bg="$green9"
                  color="white"
                  onPress={handleCallDriver}
                  icon={<Ionicons name="call" size={16} color="white" />}
                >
                  Call
                </Button>
              </XStack>
            </YStack>
          </Card>

          {/* Order Summary */}
          <Card p="$4" br="$4">
            <YStack gap="$3">
              <H6>Order Summary</H6>
              {order.items.map((item, index) => (
                <XStack key={index} justifyContent="space-between">
                  <Text>{item.quantity}x {item.name}</Text>
                  <Text fontWeight="500">${item.price * item.quantity}</Text>
                </XStack>
              ))}
              <Separator />
              <XStack justifyContent="space-between">
                <Text fontWeight="600">Total</Text>
                <Text fontWeight="600" fontSize="$5">${order.total}</Text>
              </XStack>
            </YStack>
          </Card>
        </YStack>
      </ScrollView>
    </>
  );
}
