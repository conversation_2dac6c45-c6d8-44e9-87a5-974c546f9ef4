import { Card, YStack, H2, H4, XStack, Paragraph, Spacer, Input, Spinner, Text } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, View as RNView, ScrollView, Modal, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AIChatGUI } from './AIChatGUI';
import { getServices, Service } from '../../services/apiService';
import { useCurrentUserData } from '../useCurrentUserData';

import { useLanguageStore } from '../../stores/languageStore';

export const CustomerHomeGUI = () => {
    const { t, ready } = useTranslation();
    const { isRTL } = useLanguageStore();
    const router = useRouter();

    // Don't render until translations are ready
    if (!ready) {
        return null;
    }
    const { user } = useCurrentUserData();
    const [isAIChatModalVisible, setIsAIChatModalVisible] = useState(false);
    const [services, setServices] = useState<Service[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');

    // Fetch services from backend
    useEffect(() => {
        const fetchServices = async () => {
            try {
                setLoading(true);
                setError(null);
                const servicesData = await getServices();
                setServices(servicesData);
            } catch (err) {
                console.error('Error fetching services:', err);
                setError('Failed to load services. Please try again.');
                // Fallback to empty array to prevent crashes
                setServices([]);
            } finally {
                setLoading(false);
            }
        };

        fetchServices();
    }, []);

    const filteredServices = useMemo(() => {
        if (!searchQuery.trim()) return services;
        return services.filter(service =>
            service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            service.description.toLowerCase().includes(searchQuery.toLowerCase())
        );
    }, [services, searchQuery]);

    const handleServicePress = (key: string, route: string) => {
        if (key === 'customDelivery') {
            // Open AI Chat modal instead of navigating
            setIsAIChatModalVisible(true);
        } else {
            // Navigate normally for other services
            router.push(route as any);
        }
    };

    const handleRetry = () => {
        setError(null);
        setLoading(true);
        // Re-trigger the useEffect
        getServices()
            .then(setServices)
            .catch((err) => {
                console.error('Error retrying services fetch:', err);
                setError('Failed to load services. Please try again.');
                setServices([]);
            })
            .finally(() => setLoading(false));
    };

    return (
        <>
            <YStack f={1} bg="$background" padding="$4" paddingTop="$8" paddingBottom="0" gap="$4" width={'100%'} height={'100%'}>
                        {/* Header */}
                        <YStack gap="$2">
                            <H2>{t('home.welcomeBack', {
                                name: user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : (user?.email ? user.email.split('@')[0] : 'User'),
                                defaultValue: 'Welcome back, {{name}} 👋'
                            })}</H2>
                            <XStack ai="center" gap="$1">
                            <Ionicons name="location-outline" size={16} />
                            <Paragraph size="$2" color="$gray10">
                                {t('home.location', { defaultValue: 'Nablus, Palestine' })}
                            </Paragraph>
                            </XStack>
                        </YStack>

                        {/* Search */}
                        <Input
                            placeholder={t('home.searchPlaceholder', { defaultValue: 'Search services, suppliers, or items…' })}
                            size="$3"
                            borderWidth={1}
                            bw="$0.5"
                            value={searchQuery}
                            onChangeText={setSearchQuery}
                        />

                        {/* Services Grid */}
                        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 40 }}>
                            {loading ? (
                                <YStack ai="center" jc="center" py="$8">
                                    <Spinner size="large" color="$blue10" />
                                    <Text mt="$3" color="$gray10">{t('home.loadingServices', { defaultValue: 'Loading services...' })}</Text>
                                </YStack>
                            ) : error ? (
                                <YStack ai="center" jc="center" py="$8" gap="$3">
                                    <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
                                    <Text ta="center" color="$red10" fontSize="$4">
                                        {t('home.failedToLoad', { defaultValue: 'Failed to load services. Please try again.' })}
                                    </Text>
                                    <Pressable
                                        onPress={handleRetry}
                                        style={({ pressed }) => ({
                                            backgroundColor: '#4A90E2',
                                            paddingHorizontal: 20,
                                            paddingVertical: 10,
                                            borderRadius: 8,
                                            transform: pressed ? [{ scale: 0.95 }] : [{ scale: 1 }]
                                        })}
                                    >
                                        <Text color="white" fontWeight="600">{t('home.retry', { defaultValue: 'Retry' })}</Text>
                                    </Pressable>
                                </YStack>
                            ) : filteredServices.length === 0 ? (
                                <YStack ai="center" jc="center" py="$8">
                                    <Ionicons name="cube-outline" size={48} color="#CCCCCC" />
                                    <Text mt="$3" color="$gray10">
                                        {searchQuery.trim()
                                            ? t('home.noSearchResults', { defaultValue: 'No services found matching your search' })
                                            : t('home.noServicesAvailable', { defaultValue: 'No services available' })
                                        }
                                    </Text>
                                </YStack>
                            ) : (
                                <XStack fw="wrap" jc="center" gap="$3">
                                {filteredServices.map(({ key, label, icon, color, route }) => (
                                    <Pressable
                                      key={key}
                                      onPress={() => handleServicePress(key, route)}
                                      style={({ pressed, hovered }) => ({
                                          width: '48%',
                                          transform: pressed ? [{ scale: 0.95 }] : hovered ? [{ scale: 1.02 }] : [{ scale: 1 }]
                                      })}
                                    >
                                      <Card
                                        pointerEvents="none"     // 👈 Card no longer blocks the tap
                                        p="$3"
                                        br="$4"
                                        bw="$0.5"
                                        boc="$gray5"
                                        ai="center"
                                        animation="quick"
                                        backgroundColor="#8F3DD2"
                                        hoverStyle={{ scale: 0.97 }}
                                        pressStyle={{ scale: 0.94 }}
                                      >
                                        <Ionicons name={icon as any} size={32} color={color} />
                                        <Spacer size="$2" />
                                        <H4 ta="center" color="white">
                                          {t(`services.${key}`, { defaultValue: label })}
                                        </H4>
                                      </Card>
                                    </Pressable>
                                ))}
                                </XStack>
                            )}
                        </ScrollView>
            </YStack>

            {/* AI Chat Modal */}
            <Modal
              visible={isAIChatModalVisible}
              animationType="slide"
              presentationStyle="fullScreen"
              onRequestClose={() => setIsAIChatModalVisible(false)}
            >
              <AIChatGUI onClose={() => setIsAIChatModalVisible(false)} />
            </Modal>
        </>
    );
};