const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */

const config = getDefaultConfig(__dirname);

// Add platform-specific extensions for better web support
config.resolver.platforms = ['web', 'ios', 'android', 'native'];

// Add sourceExts to handle platform-specific files
config.resolver.sourceExts = [...config.resolver.sourceExts, 'web.tsx', 'web.ts', 'web.jsx', 'web.js'];

// Ensure proper resolution for web platform
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Add web support for react-native-maps with platform-specific resolution
config.resolver.alias = {
  'react-native-maps': 'react-native-web-maps',
  // Fix framer-motion/tslib issue on web by providing a mock
  'framer-motion': require.resolve('./web-mocks/framer-motion-mock.js'),
};

// Custom resolver to handle react-native-maps and framer-motion imports on web
config.resolver.resolveRequest = (context, moduleName, platform) => {
  if (platform === 'web' && moduleName === 'react-native-maps') {
    try {
      const webMapsPath = require.resolve('react-native-web-maps');
      return {
        filePath: webMapsPath,
        type: 'sourceFile',
      };
    } catch (error) {
      console.warn('react-native-web-maps not found, falling back to default resolution');
    }
  }

  // Handle framer-motion on web platform
  if (platform === 'web' && moduleName === 'framer-motion') {
    try {
      const mockPath = path.resolve(__dirname, 'web-mocks/framer-motion-mock.js');
      return {
        filePath: mockPath,
        type: 'sourceFile',
      };
    } catch (error) {
      console.warn('framer-motion mock not found, falling back to default resolution');
    }
  }

  // Use default resolver for all other cases
  return context.resolveRequest(context, moduleName, platform);
};

// Add transformer configuration to handle import.meta polyfill
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve('./metro-transformer.js'),
};

module.exports = config;
