import express from 'express';
import { Response } from 'express';
import { authenticate } from '../middleware/auth';
import { AIService } from '../services/aiService';
import { AuthenticatedRequest } from '../types';

const router = express.Router();
const aiService = new AIService();

// Interface for chat message request
interface ChatMessageRequest {
  message: string;
  conversationId?: string;
  context?: any;
}

// Interface for chat response
interface ChatResponse {
  success: boolean;
  message?: string;
  conversationId?: string;
  suggestions?: string[];
  quickActions?: any[];
  error?: string;
}

/**
 * @route   POST /api/ai-chat/send-message
 * @desc    Send a message to AI assistant (frontend compatible)
 * @access  Public (for now, can add auth later)
 */
router.post('/send-message', async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { conversationId, message, context, messages } = req.body;

    if (!message || !message.trim()) {
      res.status(400).json({
        success: false,
        error: 'Message is required'
      });
      return;
    }

    // Use the AI service to get response
    const aiResponse = await aiService.sendMessage(
      conversationId || 'default-conversation',
      message.trim(),
      context
    );

    if (!aiResponse.success) {
      res.status(500).json({
        success: false,
        error: aiResponse.error || 'AI service error'
      });
      return;
    }

    res.json({
      success: true,
      message: aiResponse.message,
      conversationId: aiResponse.conversationId
    });

  } catch (error) {
    console.error('AI Send Message Error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/ai-chat/message
 * @desc    Send a message to AI assistant
 * @access  Private
 */
router.post('/message', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { message, conversationId, context }: ChatMessageRequest = req.body;
    const userId = req.user?.id;

    if (!message || !message.trim()) {
      res.status(400).json({
        success: false,
        error: 'Message is required'
      });
      return;
    }

    // Get or create conversation
    let currentConversationId = conversationId;
    if (!currentConversationId) {
      currentConversationId = await aiService.createConversation(userId);
    }

    // Send message to AI service
    const aiResponse = await aiService.sendMessage(
      currentConversationId,
      message.trim(),
      context
    );

    if (!aiResponse.success) {
      res.status(500).json({
        success: false,
        error: aiResponse.error || 'AI service error'
      });
      return;
    }

    const response: ChatResponse = {
      success: true,
      message: aiResponse.message,
      conversationId: currentConversationId,
      suggestions: [],
      quickActions: []
    };

    res.json(response);

  } catch (error) {
    console.error('AI Chat Error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/ai-chat/conversations
 * @desc    Get user's chat conversations
 * @access  Private
 */
router.get('/conversations', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const conversations = await aiService.getUserConversations(userId);

    res.json({
      success: true,
      conversations
    });

  } catch (error) {
    console.error('Get Conversations Error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversations'
    });
  }
});

/**
 * @route   GET /api/ai-chat/conversation/:id
 * @desc    Get specific conversation
 * @access  Private
 */
router.get('/conversation/:id', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const conversationId = req.params.id;

    const conversation = await aiService.getConversation(conversationId, userId);

    if (!conversation) {
      res.status(404).json({
        success: false,
        error: 'Conversation not found'
      });
      return;
    }

    res.json({
      success: true,
      conversation
    });

  } catch (error) {
    console.error('Get Conversation Error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversation'
    });
  }
});

/**
 * @route   DELETE /api/ai-chat/conversation/:id
 * @desc    Delete a conversation
 * @access  Private
 */
router.delete('/conversation/:id', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const conversationId = req.params.id;

    const deleted = await aiService.deleteConversation(conversationId, userId);

    if (!deleted) {
      res.status(404).json({
        success: false,
        error: 'Conversation not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Conversation deleted successfully'
    });

  } catch (error) {
    console.error('Delete Conversation Error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete conversation'
    });
  }
});

/**
 * @route   POST /api/ai-chat/feedback
 * @desc    Submit feedback for AI response
 * @access  Private
 */
router.post('/feedback', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { conversationId, messageId, rating, feedback } = req.body;
    const userId = req.user?.id;

    if (!conversationId || !messageId || rating === undefined) {
      res.status(400).json({
        success: false,
        error: 'Conversation ID, message ID, and rating are required'
      });
      return;
    }

    await aiService.submitFeedback(conversationId, messageId, {
      userId,
      rating,
      feedback,
      timestamp: new Date()
    });

    res.json({
      success: true,
      message: 'Feedback submitted successfully'
    });

  } catch (error) {
    console.error('Submit Feedback Error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to submit feedback'
    });
  }
});

/**
 * @route   GET /api/ai-chat/suggestions
 * @desc    Get personalized suggestions for user
 * @access  Private
 */
router.get('/suggestions', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    // For now, return basic suggestions
    const suggestions = [
      "How can I track my order?",
      "What are your delivery hours?",
      "How do I cancel an order?",
      "What payment methods do you accept?"
    ];

    res.json({
      success: true,
      suggestions
    });

  } catch (error) {
    console.error('Get Suggestions Error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch suggestions'
    });
  }
});

/**
 * @route   POST /api/ai-chat/context
 * @desc    Update user context for better AI responses
 * @access  Private
 */
router.post('/context', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { location, preferences, currentActivity } = req.body;

    // For now, just acknowledge the context update
    res.json({
      success: true,
      message: 'User context updated successfully'
    });

  } catch (error) {
    console.error('Update Context Error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update context'
    });
  }
});

export default router;
