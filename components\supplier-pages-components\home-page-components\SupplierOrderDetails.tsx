import { useLocalSearchPara<PERSON>, use<PERSON><PERSON><PERSON>, <PERSON>ack } from 'expo-router';
import { ScrollView } from 'react-native';
import {
  Text,
  View,
  YStack,
  XStack,
  H4,
  H6,
  Separator,
  <PERSON><PERSON>,
  Card,
  Paragraph,
} from 'tamagui';
import { useMyOrdersStore } from '~/components/customer-pages-components/orders-page-components/useMyOrdersStore';
import { Ionicons } from '@expo/vector-icons';
import { Dimensions } from 'react-native';

export default function SupplierOrderDetails() {
  const { orderId } = useLocalSearchParams<{ orderId: string }>();
  const router = useRouter();
  const order = useMyOrdersStore((s) => s.orders.find((o) => o.id === orderId));

  const windowWidth = Dimensions.get('window').width;

  if (!order) {
    return (
      <View flex={1} ai="center" jc="center">
        <Text>Order not found.</Text>
      </View>
    );
  }

  const handleMarkDelivered = () => {
    useMyOrdersStore.setState((state) => ({
      orders: state.orders.map((o) =>
        o.id === order.id ? { ...o, supplierRecievedMoney: true } : o
      ),
    }));
    router.back();
  }

  const handleMarkOnTheWay = () => {
    useMyOrdersStore.setState((state) => ({
      orders: state.orders.map((o) =>
        o.id === order.id ? { ...o, status: 'On the Way' } : o
      ),
    }));
    router.back();
  }

  const statusColor =
    order.status === 'Preparing'
      ? '$yellow7'
      : order.status === 'On the Way'
      ? '$orange6'
      : order.status === 'Delivered' 
      ? '$green6' 
      : '$red7';

  return (
    <>
      <Stack.Screen options={{ title: `Order #${orderId}`, headerShown: true }} />

      <ScrollView contentContainerStyle={{ padding: 16, paddingBottom: 120, width: windowWidth }}>
        <YStack gap="$5">
          {/* Header */}
          <Card bg="$primary" p="$4" br="$8" elevate>
            <YStack gap="$2">
              <H4 color="white">Order #{order.id}</H4>
              <Text color="white">
                Placed: {new Date(order.createdAt).toLocaleString()}
              </Text>
              <Text
                mt="$2"
                px="$3"
                py="$1"
                fontWeight="bold"
                color="white"
                fontSize="$2"
                bg={statusColor}
                br="$6"
                alignSelf="flex-start"
              >
                {order.status}
              </Text>
            </YStack>
          </Card>

          {/* Customer Info */}
          <Card elevate p="$4" bordered gap="$2">
            <XStack ai="center" gap="$2" mb="$1">
              <Ionicons name="person-circle-outline" size={20} color="#4B5563" />
              <H6>Customer Info</H6>
            </XStack>
            <Paragraph>{order.address?.address}</Paragraph>
            <Text color="$gray10">Phone: {order.phone}</Text>
            <Text color="$gray10">Note: {order.note || 'No notes provided'}</Text>
          </Card>

          {/* Order Items */}
          <YStack gap="$2">
            <XStack ai="center" gap="$2" mb="$1">
              <Ionicons name="cube-outline" size={20} color="#4B5563" />
              <H6>Order Items</H6>
            </XStack>
            {order.items.map((item, idx) => (
              <Card key={idx} bg="$gray2" p="$3" br="$6" bordered>
                <XStack jc="space-between">
                  <Text>{item.product.name}</Text>
                  <Text>
                    {item.qty} × ${item.finalPrice.toFixed(2)}
                  </Text>
                </XStack>
              </Card>
            ))}
          </YStack>

          {/* Summary */}
          <Card elevate p="$4" bordered gap="$2">
            <XStack ai="center" gap="$2" mb="$1">
              <Ionicons name="document-text-outline" size={20} color="#4B5563" />
              <H6>Payment Summary</H6>
            </XStack>
            <Text>Subtotal: ${order.subTotal.toFixed(2)}</Text>
            <Text>Delivery Fee: ${order.deliveryFee.toFixed(2)}</Text>
            {order.promo && <Text>Promo Code: {order.promo}</Text>}
            <Text fontWeight="bold" fontSize="$5" mt="$2">
              Total: ${order.total.toFixed(2)}
            </Text>
          </Card>

          {/* CTA */}
          {order.status === 'Delivered' && order.paymentMethod === 'cash' && (
            <Button
              size="$5"
              br="$6"
              bg="$secondary"
              color="white"
              icon={<Ionicons name="checkmark-done-outline" size={20} color="white" />}
              onPress={handleMarkDelivered}
              hoverStyle={{ bg: "$secondary_strong" }} 
              pressStyle={{ bg: "$secondary_strong" }}
            >
              Recieved Money
            </Button>
          )}

          {order.status === 'Preparing' && (
            <Button
              size="$5"
              br="$6"
              bg="$secondary"
              color="white"
              icon={<Ionicons name="car-outline" size={20} color="white" />}
              onPress={handleMarkOnTheWay}
              hoverStyle={{ bg: "$secondary_strong" }} 
              pressStyle={{ bg: "$secondary_strong" }}
            >
              Mark On The Way
            </Button>
          )}

          {order.status === 'On the Way' && (
            <Button
              size="$5"
              br="$6"
              bg="$primary"
              color="white"
              icon={<Ionicons name="locate-outline" size={20} color="white" />}
              onPress={() => router.push({
                pathname: '/(supplier-pages)/home/<USER>',
                params: { orderId: orderId }
              })}
              hoverStyle={{ bg: "$third" }} 
              pressStyle={{ bg: "$third" }}
            >
              Track Delivery
            </Button>
          )}
        </YStack>
      </ScrollView>
    </>
  )
}
