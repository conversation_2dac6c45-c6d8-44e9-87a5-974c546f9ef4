import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, YStack } from 'tamagui';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import { useSetSendPackage } from './useSendPackageStore';
import { useUpdateRequestPickup } from './useRequestPickupStore';
import { useSetOrderAddress } from '../useLastOrderStore';
import { Dimensions } from 'react-native';
// Use react-native-web-maps directly for web
import MapView, { Marker } from 'react-native-web-maps';

export default function SelectLocation() {
  const router = useRouter();
  const { type } = useLocalSearchParams();
  const [region, setRegion] = useState<[number, number]>([35.2544, 32.2211]); // [lng, lat] - <PERSON>blus, Palestine
  const [marker, setMarker] = useState<[number, number] | null>(null);
  const [loading, setLoading] = useState(true);
  const { width, height } = Dimensions.get('window');

  const setSendPackage = useSetSendPackage();
  const updateRequestPickup = useUpdateRequestPickup();
  const setOrderAddress = useSetOrderAddress();

  useEffect(() => {
    getCurrentLocation();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setLoading(false);
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const newRegion: [number, number] = [location.coords.longitude, location.coords.latitude];
      setRegion(newRegion);
      setMarker(newRegion);
    } catch (error) {
      console.error('Error getting location:', error);
    } finally {
      setLoading(false);
    }
  };

  const onMapPress = (coords: [number, number]) => {
    setMarker(coords);
  };

  const handleConfirm = () => {
    if (!marker) return;

    const locationData = {
      coordinates: marker,
      address: `${marker[1].toFixed(6)}, ${marker[0].toFixed(6)}`, // lat, lng for display
    };

    if (type === 'send-package-pickup') {
      setSendPackage({ pickupLocation: locationData });
      router.push('/home/<USER>/send-package-delivery');
    } else if (type === 'send-package-delivery') {
      setSendPackage({ deliveryLocation: locationData });
      router.push('/home/<USER>/send-package-summary');
    } else if (type === 'request-pickup') {
      updateRequestPickup({ pickupLocation: locationData });
      router.push('/home/<USER>/request-pickup-summary');
    } else if (type === 'order-address') {
      setOrderAddress(locationData);
      router.back();
    }
  };

  if (loading) {
    return (
      <View flex={1} justifyContent="center" alignItems="center">
        <Spinner size="large" />
        <Text mt="$4">Getting your location...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      {/* Web Map for Location Selection */}
      <MapView
        style={{ width, height }}
        region={{
          latitude: region[1],
          longitude: region[0],
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
        onPress={(e) => {
          const { latitude, longitude } = e.nativeEvent.coordinate;
          onMapPress([longitude, latitude]);
        }}
        showsUserLocation={true}
        showsMyLocationButton={true}
      >
        {/* Selected Marker */}
        {marker && (
          <Marker
            coordinate={{
              latitude: marker[1],
              longitude: marker[0],
            }}
            pinColor="purple"
          />
        )}
      </MapView>

      {/* Confirm Button */}
      <View
        position="absolute"
        bottom={20}
        left={20}
        right={20}
        zIndex={1000}
      >
        <YStack gap="$2">
          <Button
            size="$5"
            bg="$primary"
            color="white"
            disabled={!marker}
            onPress={handleConfirm}
            icon={<Ionicons name="checkmark" size={20} color="white" />}
          >
            Confirm Location
          </Button>
          <Button
            size="$4"
            variant="outlined"
            onPress={() => router.back()}
          >
            Cancel
          </Button>
        </YStack>
      </View>
    </View>
  );
}
