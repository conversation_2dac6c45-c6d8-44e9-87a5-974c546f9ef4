// stores/useSupplierProducts.ts
import { create } from 'zustand';

type Addition = {
  id: string;
  name: string;
  price: number;
};

type Product = {
  id: string;
  name: string;
  image: string;
  price: number;
  category: string;
  discountPrice?: number;
  restaurantOptions?: {
    additions?: Addition[];
    without?: string[];
    sides?: Addition[];
  };
  clothingOptions?: {
    sizes: string[];
    colors: string[];
    gallery: string[];
  };
  customOptions?: {
    id: string;
    title: string;
    type: 'text' | 'number' | 'select' | 'multi-select';
    values?: string[]; // for select types
  }[];
};

type SupplierProductsStore = {
  products: Product[];
  setProducts: (products: Product[]) => void;
  addProduct: (product: Product) => void;
  updateProduct: (id: string, updated: Partial<Product>) => void;
  deleteProduct: (id: string) => void;
};

export const useSupplierProducts = create<SupplierProductsStore>((set) => ({
  products: [],
  setProducts: (products) => set({ products }),
  addProduct: (product) => set((state) => ({ products: [product, ...state.products] })),
  updateProduct: (id, updated) =>
    set((state) => ({
      products: state.products.map((p) => (p.id === id ? { ...p, ...updated } : p)),
    })),
  deleteProduct: (id) =>
    set((state) => ({
      products: state.products.filter((p) => p.id !== id),
    })),
}));
