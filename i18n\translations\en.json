{"common": {"welcome": "Welcome", "back": "Back", "next": "Next", "previous": "Previous", "continue": "Continue", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "retry": "Retry", "close": "Close", "search": "Search", "filter": "Filter", "sort": "Sort", "submit": "Submit", "reset": "Reset", "send": "Send", "verify": "Verify", "done": "Done", "ok": "OK", "yes": "Yes", "no": "No", "allow": "Allow", "sending": "Sending...", "verifying": "Verifying...", "allFieldsRequired": "* All fields are required"}, "auth": {"login": "<PERSON><PERSON>", "signup": "Sign Up", "logout": "Logout", "welcomeBack": "Welcome Back!", "signInToContinue": "Sign in to continue your journey", "usernameOrEmail": "Username or Email", "enterUsernameOrEmail": "Enter your username or email", "password": "Password", "enterPassword": "Enter your password", "forgotPassword": "Forgot Password? Click Here", "loginFailed": "Login Failed", "invalidCredentials": "Invalid email or password.", "networkError": "Network error occurred", "firstName": "First Name", "enterFirstName": "Enter your first name", "lastName": "Last Name", "enterLastName": "Enter your last name", "emailAddress": "Email Address", "enterEmailAddress": "Enter your email address", "phone": "Phone Number", "enterPhone": "Enter your phone number", "confirmPassword": "Confirm Password", "enterConfirmPassword": "Confirm your password", "username": "Username", "enterUsername": "Enter your username", "dateOfBirth": "Date of Birth", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "address": "Address", "enterAddress": "Enter your address", "city": "City", "enterCity": "Enter your city", "country": "Country", "enterCountry": "Enter your country", "userType": "User Type", "customer": "Customer", "supplier": "Supplier", "customerDescription": "I want to order from suppliers", "supplierDescription": "I want to sell products/services", "storeName": "Store Name", "enterStoreName": "Enter your store name", "businessType": "Business Type", "enterBusinessType": "Enter your business type", "openHours": "Opening Hours", "enterOpenHours": "Enter your opening hours", "notifications": "Enable Notifications", "terms": "I accept the terms and conditions", "termsRequired": "Please accept the terms and conditions to continue.", "accountCreated": "Your account has been created successfully. Welcome to Wasel!", "resetPassword": "Reset Password", "resetYourPassword": "Reset Your Password", "identityVerified": "Your identity has been verified. Enter your new password below.", "newPassword": "New Password", "enterNewPassword": "Enter your new password", "passwordResetSuccess": "Password reset successful! You can now login with your new password.", "forgotPasswordTitle": "Forgot Password", "forgotPasswordDescription": "Enter your email address and we'll send you a verification code to reset your password.", "enterEmail": "Enter your email address", "sendCode": "Send Verification Code", "verificationCode": "Verification Code", "enterVerificationCode": "Enter the 6-digit code sent to your email", "codeNotReceived": "Didn't receive the code?", "resendCode": "Resend Code", "verifyCode": "Verify Code", "invalidCode": "Invalid or expired verification code", "emailNotFound": "No account found with this email address", "pleaseEnterCredentials": "Please enter both email and password.", "signInToAccount": "Sign In to Your Account", "signingIn": "Signing In...", "noAccount": "Don't have an account? Create one now!", "demoLogin": "<PERSON><PERSON> (<PERSON><PERSON>nti<PERSON>)", "codeSentMessage": "A 6-digit verification code has been sent to your email. Please check your email.", "codeSentTo": "We've sent a 6-digit code to your email", "enterVerificationCodeTitle": "Enter Verification Code", "enterSixDigitCode": "Enter 6-digit code", "changeEmail": "Change Email", "backToLogin": "Back to Login", "invalidAccess": "Invalid Access", "goThroughForgotPassword": "Please go through the forgot password process to reset your password.", "confirmNewPassword": "Confirm New Password", "invalidPassword": "Invalid Password", "invalidVerificationInfo": "Invalid verification information", "passwordResetSuccessMessage": "Your password has been reset successfully. You can now login with your new password.", "failedToResetPassword": "Failed to reset password", "resetPasswordError": "Something went wrong. Please try again or request a new reset link.", "passwordRequirements": "Password requirements:", "passwordRequirementsList": "• At least 6 characters long\n• Contains uppercase and lowercase letters\n• Contains at least one number", "resetting": "Resetting...", "alreadyHaveAccount": "Already have an account?"}, "signup": {"stepTitles": {"basicInfo": "Basic Information", "contactSecurity": "Contact & Security", "profileSetup": "Profile Setup", "addressInfo": "Address Information", "businessInfo": "Business Information", "finalStep": "Final Step", "signUp": "Sign Up"}, "basicInfo": {"title": "Let's get to know you", "subtitle": "Please provide your basic information to get started"}, "contactSecurity": {"title": "Contact & Security", "subtitle": "Add your contact details and create a secure password", "phoneNumber": "Phone Number", "enterPhone": "Enter your phone number", "createPassword": "Create a password (min 6 characters)", "confirmPassword": "Confirm your password", "passwordMinLength": "Password must be at least 6 characters long", "passwordsDoNotMatch": "Passwords do not match"}, "profileSetup": {"title": "Profile Setup", "subtitle": "Complete your profile with additional details", "username": "Username", "chooseUsername": "Choose a unique username", "dateOfBirth": "Date of Birth", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other"}, "addressInfo": {"title": "Address & Account Type", "subtitle": "Tell us where you are and how you'll use <PERSON><PERSON>", "address": "Address", "enterAddress": "Enter your full address", "city": "City", "enterCity": "Enter your city", "country": "Country", "enterCountry": "Enter your country", "accountType": "Account Type", "customer": "Customer", "supplier": "Supplier", "customerDescription": "I want to order from suppliers", "supplierDescription": "I want to sell products/services"}, "businessInfo": {"title": "Business Information", "subtitle": "Tell us about your business", "storeName": "Store/Business Name", "enterBusinessName": "Enter your business name", "operatingHours": "Operating Hours", "operatingHoursPlaceholder": "e.g. 9:00 AM - 11:00 PM"}, "finalStep": {"title": "Almost Done!", "subtitle": "Just a few final preferences and you're all set", "enableLocation": "Enable Location Services", "locationDescription": "Allow location access for better delivery experience", "locationGranted": "Location access granted ✓", "permissionDenied": "Permission Denied", "locationOptional": "Location access is optional but recommended for better service.", "locationError": "Could not get location. You can set it later in your profile.", "enableNotifications": "Enable Notifications", "notificationDescription": "Get updates about your orders and deliveries", "agreeToTerms": "I agree to the Terms of Service and Privacy Policy. I understand that my data will be processed according to these terms.", "accountSummary": "Account Summary", "name": "Name", "email": "Email", "accountType": "Account Type", "business": "Business", "customer": "Customer", "supplier": "Supplier", "completingSignup": "By completing signup, you agree to our terms and conditions"}, "navigation": {"next": "Next", "previous": "Previous", "finish": "Create Account", "login": "<PERSON><PERSON>", "creating": "Creating Account..."}, "errors": {"signupFailed": "Signup Failed", "failedToCreate": "Failed to create account. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again."}}, "navigation": {"home": "Home", "orders": "Orders", "packages": "Packages", "profile": "Profile", "map": "Map", "suppliers": "Suppliers", "products": "Products", "aiAssistant": "AI Assistant"}, "home": {"welcomeBack": "Welcome back, {{name}} 👋", "location": "Nablus, Palestine", "searchPlaceholder": "Search services, suppliers, or items…", "quickActions": "Quick Actions", "services": "Our Services", "promotions": "Special Offers", "recentOrders": "Recent Orders", "popularSuppliers": "Popular Suppliers", "categories": "Categories", "foodDelivery": "Food Delivery", "groceries": "Groceries", "pharmacy": "Pharmacy", "electronics": "Electronics", "clothing": "Clothing", "homeGarden": "Home & Garden", "sendPackage": "Send Package", "requestPickup": "Request Pickup", "trackOrder": "Track Order", "customerSupport": "Customer Support", "aiChatSystem": "AI Chat System", "loadingServices": "Loading services...", "noServicesAvailable": "No services available", "noSearchResults": "No services found matching your search", "retryLoading": "Retry"}, "services": {"shopOrder": "Order from Supplier", "sendPackage": "Send a Package", "requestPickup": "Request Pickup", "customDelivery": "AI Chat System", "trackOrders": "Track My Orders", "trackPackages": "Track My Packages", "support": "Contact Support", "failedToLoad": "Failed to load services. Please try again.", "retry": "Retry"}, "profile": {"myProfile": "My Profile", "editProfile": "Edit Profile", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "addressInfo": "Address Information", "accountSettings": "Account <PERSON><PERSON>", "language": "Language", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "helpSupport": "Help & Support", "aboutApp": "About App", "version": "Version", "updateProfile": "Update Profile", "profileUpdated": "Profile updated successfully!", "profileUpdateFailed": "Failed to update profile. Please try again.", "changeLanguage": "Change Language", "english": "English", "arabic": "العربية", "failedToLoad": "Failed to load profile", "updatePersonalInfo": "Update your personal information", "updatePassword": "Update your password", "accountStatus": "Account Status", "signOutAccount": "Sign out of your account", "currentPassword": "Current Password", "enterCurrentPassword": "Enter current password"}, "orders": {"myOrders": "My Orders", "orderHistory": "Order History", "activeOrders": "Active Orders", "orderDetails": "Order Details", "orderNumber": "Order #{{number}}", "orderDate": "Order Date", "orderTime": "Order Time", "totalAmount": "Total Amount", "deliveryAddress": "Delivery Address", "paymentMethod": "Payment Method", "orderStatus": "Order Status", "trackOrder": "Track Order", "reorder": "Reorder", "cancelOrder": "Cancel Order", "viewDetails": "View Details", "noOrdersYet": "You have no orders yet.", "startShopping": "Start Shopping", "items": "Items", "filters": {"all": "All", "pending": "Pending", "confirmed": "Confirmed", "preparing": "Preparing", "ready": "Ready", "onTheWay": "On the Way", "delivered": "Delivered", "cancelled": "Cancelled"}, "statuses": {"pending": "Pending", "confirmed": "Confirmed", "preparing": "Preparing", "ready": "Ready for Pickup", "out_for_delivery": "On the Way", "delivered": "Delivered", "cancelled": "Cancelled"}}, "chat": {"aiAssistant": "AI Assistant", "chatWithAI": "Chat with AI", "typeMessage": "Type your message...", "send": "Send", "quickActions": "Quick Actions", "howCanIHelp": "How can I help you today?", "welcomeMessage": "Hello! I'm your AI assistant. I can help you with orders, deliveries, recommendations, and more. What would you like to do?", "suggestions": {"trackOrder": "Track Order", "foodDelivery": "Food Delivery", "groceries": "Groceries", "pharmacy": "Pharmacy", "sendPackage": "Send Package", "requestPickup": "Request Pickup", "orderHistory": "Order History", "customerSupport": "Customer Support"}, "smartSuggestions": {"title": "Smart Suggestions", "lunchTime": "What's for lunch today? Show me restaurants", "lunchSpecials": "Any lunch specials available?", "quickFood": "Quick food delivery options near me", "dinnerRecommendations": "Dinner recommendations for tonight", "dinnerRestaurants": "What restaurants are open for dinner?", "familyMeals": "Family meal deals available", "trackOrder": "How can I track my current order?", "bestRestaurants": "What are the best restaurants near me?", "sendPackage": "How do I send a package quickly?", "deliveryHours": "What are your delivery hours and areas?", "modifyOrder": "How can I modify or cancel my order?", "paymentMethods": "What payment methods do you accept?", "specialOffers": "Any special offers or discounts today?", "customerSupport": "How do I contact customer support?", "schedulePickup": "Can I schedule a pickup for tomorrow?", "fastestDelivery": "What's the fastest delivery option?"}, "contextSuggestions": {"trackCurrentOrder": "Track my current order", "contactDriver": "Contact delivery driver", "findLunchOptions": "Find lunch options", "orderFromFavorite": "Order from favorite restaurant", "browseDinnerMenus": "Browse dinner menus", "orderGroceriesTonight": "Order groceries for tonight", "findSuppliersNearMe": "Find suppliers near me", "checkLocalPromotions": "Check local promotions", "howToPlaceFirstOrder": "How to place my first order?", "browsePopularSuppliers": "Browse popular suppliers"}, "characters": "characters", "responses": {"orderTracking": "I can help you track your orders. Let me show you your active orders...", "foodDelivery": "Great! I'll help you find the best restaurants in your area. What type of cuisine are you in the mood for?", "groceries": "I can help you find grocery stores and supermarkets nearby. What items do you need?", "pharmacy": "I'll help you locate pharmacies in your area. Do you need any specific medications?", "sendPackage": "I can help you send a package. Let me guide you through the process...", "requestPickup": "I'll help you request a pickup service. What type of items need to be picked up?", "orderHistory": "Here's your recent order history. You can view details or reorder from any previous order.", "customerSupport": "I'm here to help! What issue are you experiencing? I can assist with orders, deliveries, payments, or connect you with human support if needed."}}, "suppliers": {"suppliers": "Suppliers", "searchPlaceholder": "Search {{category}}...", "rating": "Rating", "reviews": "Reviews", "deliveryTime": "Delivery Time", "deliveryFee": "Delivery Fee", "minimumOrder": "Minimum Order", "openNow": "Open Now", "closed": "Closed", "opensAt": "Opens at {{time}}", "closesAt": "Closes at {{time}}", "viewMenu": "View Menu", "viewProducts": "View Products", "orderNow": "Order Now", "noSuppliersFound": "No suppliers found", "tryDifferentCategory": "Try browsing a different category", "popularSuppliers": "Popular Suppliers", "nearbySuppliers": "Nearby Suppliers", "featuredSuppliers": "Featured Suppliers"}, "packages": {"myPackages": "My Packages", "sendPackage": "Send Package", "packageHistory": "Package History", "activePackages": "Active Packages", "packageDetails": "Package Details", "packageNumber": "Package #{{number}}", "sender": "Sender", "recipient": "Recipient", "pickupAddress": "Pickup Address", "deliveryAddress": "Delivery Address", "packageSize": "Package Size", "weight": "Weight", "packageType": "Package Type", "fragile": "<PERSON><PERSON><PERSON>", "urgent": "<PERSON><PERSON>", "standard": "Standard", "small": "Small", "medium": "Medium", "large": "Large", "extraLarge": "Extra Large", "sentPackages": "Sent Packages", "pickupRequests": "Pickup Requests", "noPackagesYet": "No {{type}} yet.", "sendFirstPackage": "Send your first package", "requestFirstPickup": "Request your first pickup", "trackPackage": "Track Package", "packageId": "Package ID", "estimatedDelivery": "Estimated Delivery", "from": "From", "to": "To", "item": "<PERSON><PERSON>", "time": "Time", "notes": "Notes", "none": "None", "status": "Status", "driver": "Driver", "statuses": {"pending": "Pending", "confirmed": "Confirmed", "picked_up": "Picked Up", "in_transit": "In Transit", "out_for_delivery": "Out for Delivery", "delivered": "Delivered", "cancelled": "Cancelled"}}, "categories": {"chooseCategory": "Choose a Category", "searchSuppliers": "Search suppliers…", "findBest": "Find the best {{category}} near you", "promotions": "Promotions", "filter": "Filter", "noResults": "No results found", "tryDifferentSearch": "Try a different search term", "restaurants": "Restaurants", "clothings": "Clothings", "pharmacies": "Pharmacies", "supermarkets": "Supermarkets", "electronics": "Electronics", "homeGarden": "Home & Garden", "beauty": "Beauty & Personal Care", "automotive": "Automotive", "sports": "Sports & Fitness", "books": "Books & Stationery", "toys": "Toys & Games", "pets": "Pet Supplies"}, "location": {"nablus": "Nablus, Palestine"}, "errors": {"somethingWentWrong": "Something went wrong", "networkError": "Network error. Please check your connection.", "serverError": "Server error. Please try again later.", "validationError": "Please check your input and try again.", "unauthorized": "You are not authorized to perform this action.", "notFound": "The requested resource was not found.", "timeout": "Request timeout. Please try again.", "unknownError": "An unknown error occurred."}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "passwordTooShort": "Password must be at least 6 characters", "passwordsDoNotMatch": "Passwords do not match", "invalidPhone": "Please enter a valid phone number", "invalidCode": "Please enter a valid 6-digit code", "passwordLowercase": "Password must contain at least one lowercase letter", "passwordUppercase": "Password must contain at least one uppercase letter", "passwordNumber": "Password must contain at least one number"}, "supplier": {"hello": "Hello", "role": "Role", "supplier": "Supplier", "orders": "Orders", "noOrders": "No orders", "newOrders": "New Orders", "inPreparingOrders": "In Preparing Orders", "onTheWayOrders": "On The Way Orders", "deliveredOrders": "Delivered Orders", "addCategory": "Add Category", "editCategory": "Edit Category", "categoryName": "Category name"}}