import { Ionicons } from "@expo/vector-icons";
import { <PERSON><PERSON>, <PERSON>, ScrollView, XStack, YStack, Text, View, Input, Sheet, Separator } from "tamagui";
import { Image, Dimensions } from "react-native";
import { useCurrentUserData } from "~/components/useCurrentUserData";
import { suppliersData } from "~/temp-data/suppliersData";
import { MotiView } from "moti";
import { LinearGradient } from "expo-linear-gradient";
import { useSupplierProducts } from "./useSupplierProducts";
import { useEffect } from "react";
import { router } from "expo-router";
import { useSupplierCategories } from './useSupplierCategories';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function SupplierProductsGUI() {
  const { t } = useTranslation();
  const { user } = useCurrentUserData();
  const supplier = suppliersData.find((s) => s.id === user?.supplierId);
  const windowWidth = Dimensions.get("window").width;

  const { products, setProducts } = useSupplierProducts();

  useEffect(() => {
    if (supplier?.products) {
      setProducts(supplier.products);
    }
  }, [supplier]);

  const { categories, addCategory, deleteCategory, selectCategory, selectedCategory, renameCategory } = useSupplierCategories();
  const [newCatModal, setNewCatModal] = useState(false);
  const [newCat, setNewCat] = useState('');
  const [editCat, setEditCat] = useState<string | null>(null);
  const [editedValue, setEditedValue] = useState('');

  const filteredCategories = products.reduce<string[]>((acc, product) =>
    acc.includes(product.category) ? acc : [...acc, product.category],
  []
  );

  useEffect(() => {
    // Add 'All' category if not present
    if (!categories.includes('All')) {
      addCategory('All');
    }
    // Add product categories if not present
    for (const category of filteredCategories) {
      if (!categories.includes(category)) {
        addCategory(category);
      }
    }
    // Select 'All' only if nothing is selected
    if (!selectedCategory) {
      selectCategory('All');
    }
    // eslint-disable-next-line
  }, [products, categories, addCategory, selectCategory]);

  const filteredProducts = (selectedCategory && selectedCategory !== 'All')
  ? products.filter(p => p.category === selectedCategory)
  : products;

  return (
    <>
      <Sheet open={newCatModal} onOpenChange={setNewCatModal}>
        <Sheet.Frame padding="$4" gap="$3">
          <Text fontSize="$6" fontWeight="600">{t('supplier.addCategory', { defaultValue: 'Add Category' })}</Text>
          <Input value={newCat} onChangeText={setNewCat} placeholder={t('supplier.categoryName', { defaultValue: 'Category name' })} />
          <Button mt="$3" size="$3" br="$6" borderColor={"$secondary"} color={"$secondary"} variant="outlined" 
            onPress={() => {
              if (newCat.trim()) addCategory(newCat);
              setNewCat('');
              setNewCatModal(false);
            }}
          >
            {t('common.save', { defaultValue: 'Save' })}
          </Button>
          <Button mt="$3" size="$3" br="$6" borderColor={"red"} color={"red"} variant="outlined"
            onPress={() => setNewCatModal(false)}
          >
            {t('common.cancel', { defaultValue: 'Cancel' })}
          </Button>
        </Sheet.Frame>
      </Sheet>

      <Sheet open={!!editCat} onOpenChange={() => setEditCat(null)}>
        <Sheet.Frame padding="$4" gap="$3">
          <Text fontSize="$6" fontWeight="600">{t('supplier.editCategory', { defaultValue: 'Edit Category' })}</Text>
          <Input value={editedValue} onChangeText={setEditedValue} />
          <Button mt="$3" size="$3" br="$6" borderColor={"$secondary"} color={"$secondary"} variant="outlined" 
            onPress={() => {
              if (editCat && editedValue.trim()) renameCategory(editCat, editedValue);
              setEditCat(null);
              setEditedValue('');
            }}
          >
            {t('common.save', { defaultValue: 'Save' })}
          </Button>
          <Button mt="$3" size="$3" br="$6" borderColor={"red"} color={"red"} variant="outlined"
            onPress={() => setNewCatModal(false)}
          >
            {t('common.cancel', { defaultValue: 'Cancel' })}
          </Button>
        </Sheet.Frame>
      </Sheet>
    <ScrollView
      padding={16}
      width={windowWidth}
      contentContainerStyle={{ flexGrow: 1, paddingBottom: 120 }}
    >
      <LinearGradient
        colors={["#7529B3", "#8F3DD2"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{
          borderRadius: 24,
          padding: 24,
          marginBottom: 24,
        }}
      >
        <MotiView
          from={{ opacity: 0, translateY: -10 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: "timing", duration: 500 }}
        >
          <YStack>
            <XStack alignItems="center">
              <Image
                source={{ uri: supplier?.logoUrl }}
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 12,
                  borderWidth: 2,
                  borderColor: "#fff",
                  marginRight: 16,
                  backgroundColor: "#fff",
                }}
              />
              <YStack>
                <Text color="#fff" fontSize={22} fontWeight="bold">
                  {supplier?.name || "My Store"}
                </Text>
                <Text color="#eee" fontSize={14}>
                  {products.length} Product{products.length !== 1 ? "s" : ""}
                </Text>
              </YStack>
            </XStack>

            <XStack justifyContent="space-between" mt="$4">
              <Text fontSize={26} fontWeight="bold" color="#fff">
                My Products
              </Text>

              <View style={{ position: "relative" }}>
                <Ionicons name="pricetags" size={28} color="#fff" />
                <YStack
                  position="absolute"
                  top={-13}
                  right={-12}
                  bg="red"
                  borderRadius={10}
                  px="$2"
                  py="$1"
                >
                  <Text color="white" fontSize="$2">
                    {products.length}
                  </Text>
                </YStack>
              </View>
            </XStack>
          </YStack>
        </MotiView>
      </LinearGradient>

      {/* CATEGORY SECTION */}
      <XStack ai="center" jc="space-between" mb="$4" space="$2" height="$8">
          <ScrollView horizontal>
          {categories.map((cat, i) => (
            <YStack
              key={i}
              bg={selectedCategory === cat ? '$secondary' : '$gray5'}
              px="$3"
              py="$2"
              br="$4"
              ai="center"
              jc="center"
              mr="$2"
              height={"90%"}
            >
              <Text
                color={selectedCategory === cat ? 'white' : 'black'}
                fontWeight="600"
                onPress={() => selectCategory(cat)}
              >
                {cat}
              </Text>
              {cat === "All" ? (null) : (
              <XStack space="$1" mt="$1">
                <Button
                  color={selectedCategory === cat ? 'white' : 'black'}
                  icon={<Ionicons name="create-outline" size={14} />}
                  size="$2"
                  chromeless
                  onPress={() => {
                    setEditCat(cat);
                    setEditedValue(cat);
                  }}
                />
                <Button
                  icon={<Ionicons name="trash-outline" size={14} color="#f00" />}
                  size="$2"
                  chromeless
                  onPress={() => deleteCategory(cat)}
                />
              </XStack>
              )}
            </YStack>
          ))}
          </ScrollView>
          <Button
              bg="$gray5"
              icon={<Ionicons name="add-circle-outline" size={20} />}
              size="$4"
              br="$12"
              onPress={() => setNewCatModal(true)}
          >
          </Button>
      </XStack>

      {/* PRODUCTS LIST SECTION */}

      {filteredProducts.length === 0 ? (
        <YStack alignItems="center" jc="center" py="$10">
          <Ionicons name="cube-outline" size={80} color="#ccc" />
          <Text fontSize="$5" mt="$2" color="$gray9">
            No products yet.
          </Text>
        </YStack>
      ) : (
        filteredProducts.map((product, i) => (
          <MotiView
            key={product.id}
            from={{ opacity: 0, translateY: 10 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: i * 100 }}
          >
            <YStack>
              <ProductCard product={product} />
            </YStack>
          </MotiView>
        ))
      )}
    </ScrollView>
    <Button
        icon={<Ionicons name="add" size={24} color="white" />}
        position="absolute"
        bottom={20}
        right={20}
        circular
        size="$6"
        bg="$secondary"
        hoverStyle={{ bg: "$secondary_strong" }}
        pressStyle={{ bg: "$secondary_strong" }}
        zIndex={10}
        elevate
        onPress={() => router.push("/(supplier-pages)/products/add-product")}
      >
      </Button>
      </>
  );
}

type Product = {
  id: string;
  name: string;
  category: string;
  price: number;
  discountPrice?: number;
  image: string;
};

function ProductCard({ product }: { product: Product }) {
  const hasDiscount = product.discountPrice && product.discountPrice > 0;

  return (
    <Card elevate br="$8" mb="$4" p="$4" borderWidth={1} borderColor="$gray5">
      <XStack gap="$4">
        <Image
          source={{ uri: product.image }}
          style={{ width: 80, height: 80, borderRadius: 10 }}
        />
        <YStack flex={1} jc="space-between">
          <YStack>
            <Text fontWeight="bold" fontSize="$6">
              {product.name}
            </Text>
            <Text color="$gray10">{product.category}</Text>
            <XStack gap="$2" mt="$1">
              <Text color={hasDiscount ? "$red10" : "$green10"} fontWeight="600">
                {`${product.price} ₪`}
              </Text>
              {hasDiscount ? (
                <Text color="$gray9" textDecorationLine="line-through">
                  {`${product.discountPrice} ₪`}
                </Text>
              ) : (null)}
            </XStack>
          </YStack>

          <XStack jc="flex-end" gap="$3" mt="$2">
            <Button chromeless icon={<Ionicons name="create-outline" size={18} />}></Button>
            <Button chromeless icon={<Ionicons name="trash-outline" size={18} color="#f00" />}></Button>
          </XStack>
        </YStack>
      </XStack>
    </Card>
  );
}
