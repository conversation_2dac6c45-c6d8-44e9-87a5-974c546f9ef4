import { Tabs } from 'expo-router'
import { Ionicons } from '@expo/vector-icons'
import { View } from 'tamagui'
import { Pressable } from 'react-native'
import { useTranslation } from 'react-i18next'

export default function CustomerTabsLayout() {
  const { t } = useTranslation();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#7529B3',
        tabBarStyle: {
          backgroundColor: '#fff',
          height: 60,
          elevation: 5,
          paddingHorizontal: 10,
        },
        tabBarItemStyle: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        },
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          headerShown: false,
          title: t('navigation.home', { defaultValue: 'Home' }),
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused? "home" : "home-outline"} size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="orders"
        options={{
          headerShown: false,
          title: t('navigation.orders', { defaultValue: 'Orders' }),
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused? "receipt" : "receipt-outline"} size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="suppliers-map"
        options={({ navigation }) => ({
          headerShown: false,
          tabBarLabel: () => null,
           tabBarButton: (props) => {
            const { onPress, accessibilityState } = props;
            const isFocused = accessibilityState?.selected;

            return (
              <View
                style={{
                  top: -25,
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: 70,
                  height: 70,
                  borderRadius: 35,
                  backgroundColor: '#7529B3',
                  elevation: 10,
                }}
                onStartShouldSetResponder={() => true}
                onResponderRelease={event => onPress?.(event)}
              >
                <Pressable
                  style={{ justifyContent: 'center', alignItems: 'center', width: '100%', height: '100%' }}
                  android_ripple={{ color: 'rgba(255,255,255,0.3)', borderless: true }}
                  onPress={onPress}
                >
                  <Ionicons
                    name="map"
                    size={32}
                    color="white"
                    style={{ transform: [{ scale: isFocused ? 1.1 : 1 }] }}
                  />
                </Pressable>
              </View>
            )
          },
        })}
      />
      <Tabs.Screen
        name="packages"
        options={{
          headerShown: false,
          title: t('navigation.packages', { defaultValue: 'Packages' }),
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused? "cube-sharp" : "cube-outline"} size={size} color={color} />
          ),
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          headerShown: false,
          title: t('navigation.profile', { defaultValue: 'Profile' }),
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused? "person" : "person-outline"} size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  )
}
