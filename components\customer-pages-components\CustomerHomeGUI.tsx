import { <PERSON>, YStack, H2, H4, <PERSON><PERSON><PERSON>ck, Paragraph, Spacer, Input, Spinner, Text, Button, Separator } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, View as RNView, ScrollView, Modal, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AIChatGUI } from './AIChatGUI';
import { getServices, Service, comprehensiveSearch, SearchResult, ComprehensiveSearchResults } from '../../services/apiService';
import { useCurrentUserData } from '../useCurrentUserData';

import { useLanguageStore } from '../../stores/languageStore';

export const CustomerHomeGUI = () => {
    const { t, ready } = useTranslation();
    const { isRTL } = useLanguageStore();
    const router = useRouter();

    // Don't render until translations are ready
    if (!ready) {
        return null;
    }
    const { user } = useCurrentUserData();
    const [isAIChatModalVisible, setIsAIChatModalVisible] = useState(false);
    const [services, setServices] = useState<Service[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState<ComprehensiveSearchResults | null>(null);
    const [isSearching, setIsSearching] = useState(false);
    const [showSearchResults, setShowSearchResults] = useState(false);

    // Fetch services from backend
    useEffect(() => {
        const fetchServices = async () => {
            try {
                setLoading(true);
                setError(null);
                const servicesData = await getServices();
                setServices(servicesData);
            } catch (err) {
                console.error('Error fetching services:', err);
                setError('Failed to load services. Please try again.');
                // Fallback to empty array to prevent crashes
                setServices([]);
            } finally {
                setLoading(false);
            }
        };

        fetchServices();
    }, []);

    // Helper function to detect if text contains Arabic characters
    const containsArabic = (text: string): boolean => {
        const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
        return arabicRegex.test(text);
    };

    // Handle search functionality
    useEffect(() => {
        const performSearch = async () => {
            if (!searchQuery.trim()) {
                setSearchResults(null);
                setShowSearchResults(false);
                return;
            }

            setIsSearching(true);
            try {
                const results = await comprehensiveSearch(searchQuery);
                setSearchResults(results);
                setShowSearchResults(true);
            } catch (error) {
                console.error('Search error:', error);
                setSearchResults(null);
            } finally {
                setIsSearching(false);
            }
        };

        const timeoutId = setTimeout(performSearch, 300); // Debounce search
        return () => clearTimeout(timeoutId);
    }, [searchQuery]);

    const filteredServices = useMemo(() => {
        if (showSearchResults) return [];
        return services;
    }, [services, showSearchResults]);

    const handleServicePress = (key: string, route: string) => {
        if (key === 'customDelivery') {
            // Open AI Chat modal instead of navigating
            setIsAIChatModalVisible(true);
        } else {
            // Navigate normally for other services
            router.push(route as any);
        }
    };

    const handleSearchResultPress = (result: SearchResult) => {
        if (result.type === 'service' && result.data.key === 'customDelivery') {
            setIsAIChatModalVisible(true);
        } else {
            router.push(result.route as any);
        }
        // Clear search after navigation
        setSearchQuery('');
        setShowSearchResults(false);
    };

    const clearSearch = () => {
        setSearchQuery('');
        setSearchResults(null);
        setShowSearchResults(false);
    };

    const handleRetry = () => {
        setError(null);
        setLoading(true);
        // Re-trigger the useEffect
        getServices()
            .then(setServices)
            .catch((err) => {
                console.error('Error retrying services fetch:', err);
                setError('Failed to load services. Please try again.');
                setServices([]);
            })
            .finally(() => setLoading(false));
    };

    return (
        <>
            <YStack f={1} bg="$background" padding="$4" paddingTop="$8" paddingBottom="0" gap="$4" width={'100%'} height={'100%'}>
                        {/* Header */}
                        <YStack gap="$2">
                            <H2>{t('home.welcomeBack', {
                                name: user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : (user?.email ? user.email.split('@')[0] : 'User'),
                                defaultValue: 'Welcome back, {{name}} 👋'
                            })}</H2>
                            <XStack ai="center" gap="$1">
                            <Ionicons name="location-outline" size={16} />
                            <Paragraph size="$2" color="$gray10">
                                {t('home.location', { defaultValue: 'Nablus, Palestine' })}
                            </Paragraph>
                            </XStack>
                        </YStack>

                        {/* Search */}
                        <XStack ai="center" gap="$2">
                            <Input
                                placeholder={t('home.searchPlaceholder', { defaultValue: 'Search services, suppliers, or items…' })}
                                size="$3"
                                borderWidth={1}
                                bw="$0.5"
                                value={searchQuery}
                                onChangeText={setSearchQuery}
                                flex={1}
                            />
                            {searchQuery.trim() && (
                                <Button
                                    size="$3"
                                    icon={<Ionicons name="close" size={16} />}
                                    onPress={clearSearch}
                                    chromeless
                                />
                            )}
                        </XStack>

                        {/* Content Area */}
                        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 40 }}>
                            {showSearchResults ? (
                                // Search Results
                                <YStack gap="$4">
                                    {isSearching ? (
                                        <YStack ai="center" jc="center" py="$8">
                                            <Spinner size="large" color="$blue10" />
                                            <Text mt="$3" color="$gray10">{t('home.searching', { defaultValue: 'Searching...' })}</Text>
                                        </YStack>
                                    ) : searchResults && searchResults.total > 0 ? (
                                        <YStack gap="$4">
                                            {(() => {
                                                // Detect search language and use appropriate translations
                                                const isArabicSearch = containsArabic(searchQuery);
                                                const searchLang = isArabicSearch ? 'ar' : 'en';

                                                return (
                                                    <>
                                                        <Text fontSize="$5" fontWeight="bold">
                                                            {t('home.searchResults', { lng: searchLang, defaultValue: isArabicSearch ? 'نتائج البحث' : 'Search Results' })} ({searchResults.total})
                                                        </Text>

                                                        {/* Services Results */}
                                                        {searchResults.services.length > 0 && (
                                                            <YStack gap="$2">
                                                                <Text fontSize="$4" fontWeight="600" color="$blue10">
                                                                    {t('home.services', { lng: searchLang, defaultValue: isArabicSearch ? 'الخدمات' : 'Services' })} ({searchResults.services.length})
                                                                </Text>
                                                    <YStack gap="$2">
                                                        {searchResults.services.map((result) => (
                                                            <Pressable
                                                                key={result.id}
                                                                onPress={() => handleSearchResultPress(result)}
                                                                style={({ pressed }) => ({
                                                                    transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                                                                })}
                                                            >
                                                                <Card p="$3" br="$3" bw="$0.5" boc="$gray5">
                                                                    <XStack ai="center" gap="$3">
                                                                        <Ionicons name={result.icon as any} size={24} color={result.color} />
                                                                        <YStack flex={1}>
                                                                            <Text fontSize="$4" fontWeight="600">{result.title}</Text>
                                                                            {result.description && (
                                                                                <Text fontSize="$3" color="$gray10" numberOfLines={2}>
                                                                                    {result.description}
                                                                                </Text>
                                                                            )}
                                                                        </YStack>
                                                                        <Ionicons name="chevron-forward" size={16} color="#999" />
                                                                    </XStack>
                                                                </Card>
                                                            </Pressable>
                                                        ))}
                                                    </YStack>
                                                </YStack>
                                            )}

                                                        {/* Categories Results */}
                                                        {searchResults.categories.length > 0 && (
                                                            <YStack gap="$2">
                                                                <Text fontSize="$4" fontWeight="600" color="$green10">
                                                                    {t('home.categories', { lng: searchLang, defaultValue: isArabicSearch ? 'الفئات' : 'Categories' })} ({searchResults.categories.length})
                                                                </Text>
                                                    <YStack gap="$2">
                                                        {searchResults.categories.map((result) => (
                                                            <Pressable
                                                                key={result.id}
                                                                onPress={() => handleSearchResultPress(result)}
                                                                style={({ pressed }) => ({
                                                                    transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                                                                })}
                                                            >
                                                                <Card p="$3" br="$3" bw="$0.5" boc="$gray5">
                                                                    <XStack ai="center" gap="$3">
                                                                        <Ionicons name={result.icon as any} size={24} color={result.color} />
                                                                        <YStack flex={1}>
                                                                            <Text fontSize="$4" fontWeight="600">{result.title}</Text>
                                                                            {result.description && (
                                                                                <Text fontSize="$3" color="$gray10" numberOfLines={2}>
                                                                                    {result.description}
                                                                                </Text>
                                                                            )}
                                                                        </YStack>
                                                                        <Ionicons name="chevron-forward" size={16} color="#999" />
                                                                    </XStack>
                                                                </Card>
                                                            </Pressable>
                                                        ))}
                                                    </YStack>
                                                </YStack>
                                            )}

                                                        {/* Suppliers Results */}
                                                        {searchResults.suppliers.length > 0 && (
                                                            <YStack gap="$2">
                                                                <Text fontSize="$4" fontWeight="600" color="$orange10">
                                                                    {t('home.suppliers', { lng: searchLang, defaultValue: isArabicSearch ? 'الموردين' : 'Suppliers' })} ({searchResults.suppliers.length})
                                                                </Text>
                                                    <YStack gap="$2">
                                                        {searchResults.suppliers.map((result) => (
                                                            <Pressable
                                                                key={result.id}
                                                                onPress={() => handleSearchResultPress(result)}
                                                                style={({ pressed }) => ({
                                                                    transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                                                                })}
                                                            >
                                                                <Card p="$3" br="$3" bw="$0.5" boc="$gray5">
                                                                    <XStack ai="center" gap="$3">
                                                                        <Ionicons name="storefront" size={24} color="#FF6B35" />
                                                                        <YStack flex={1}>
                                                                            <Text fontSize="$4" fontWeight="600">{result.title}</Text>
                                                                            {result.subtitle && (
                                                                                <Text fontSize="$3" color="$blue10">{result.subtitle}</Text>
                                                                            )}
                                                                            {result.description && (
                                                                                <Text fontSize="$3" color="$gray10" numberOfLines={2}>
                                                                                    {result.description}
                                                                                </Text>
                                                                            )}
                                                                        </YStack>
                                                                        <Ionicons name="chevron-forward" size={16} color="#999" />
                                                                    </XStack>
                                                                </Card>
                                                            </Pressable>
                                                        ))}
                                                    </YStack>
                                                </YStack>
                                            )}

                                                        {/* Products Results */}
                                                        {searchResults.products.length > 0 && (
                                                            <YStack gap="$2">
                                                                <Text fontSize="$4" fontWeight="600" color="$purple10">
                                                                    {t('home.products', { lng: searchLang, defaultValue: isArabicSearch ? 'المنتجات' : 'Products' })} ({searchResults.products.length})
                                                                </Text>
                                                    <YStack gap="$2">
                                                        {searchResults.products.map((result) => (
                                                            <Pressable
                                                                key={result.id}
                                                                onPress={() => handleSearchResultPress(result)}
                                                                style={({ pressed }) => ({
                                                                    transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                                                                })}
                                                            >
                                                                <Card p="$3" br="$3" bw="$0.5" boc="$gray5">
                                                                    <XStack ai="center" gap="$3">
                                                                        <Ionicons name="cube" size={24} color="#8B5CF6" />
                                                                        <YStack flex={1}>
                                                                            <Text fontSize="$4" fontWeight="600">{result.title}</Text>
                                                                            {result.subtitle && (
                                                                                <Text fontSize="$3" color="$orange10">{result.subtitle}</Text>
                                                                            )}
                                                                            {result.description && (
                                                                                <Text fontSize="$3" color="$gray10" numberOfLines={2}>
                                                                                    {result.description}
                                                                                </Text>
                                                                            )}
                                                                        </YStack>
                                                                        <Ionicons name="chevron-forward" size={16} color="#999" />
                                                                    </XStack>
                                                                </Card>
                                                            </Pressable>
                                                        ))}
                                                    </YStack>
                                                        </YStack>
                                                        )}
                                                    </>
                                                );
                                            })()}
                                        </YStack>
                                    ) : (
                                        <YStack ai="center" jc="center" py="$8">
                                            <Ionicons name="search-outline" size={48} color="#CCCCCC" />
                                            <Text mt="$3" color="$gray10" ta="center">
                                                {(() => {
                                                    const isArabicSearch = containsArabic(searchQuery);
                                                    const searchLang = isArabicSearch ? 'ar' : 'en';
                                                    return t('home.noSearchResults', {
                                                        lng: searchLang,
                                                        defaultValue: isArabicSearch ? 'لم يتم العثور على نتائج لبحثك' : 'No results found for your search'
                                                    });
                                                })()}
                                            </Text>
                                            <Text color="$gray8" ta="center" mt="$1">
                                                {(() => {
                                                    const isArabicSearch = containsArabic(searchQuery);
                                                    const searchLang = isArabicSearch ? 'ar' : 'en';
                                                    return t('home.tryDifferentSearch', {
                                                        lng: searchLang,
                                                        defaultValue: isArabicSearch ? 'جرب البحث عن الخدمات أو الموردين أو المنتجات' : 'Try searching for services, suppliers, or products'
                                                    });
                                                })()}
                                            </Text>
                                        </YStack>
                                    )}
                                </YStack>
                            ) : (
                                // Default Services Grid
                                <>
                                    {loading ? (
                                        <YStack ai="center" jc="center" py="$8">
                                            <Spinner size="large" color="$blue10" />
                                            <Text mt="$3" color="$gray10">{t('home.loadingServices', { defaultValue: 'Loading services...' })}</Text>
                                        </YStack>
                                    ) : error ? (
                                        <YStack ai="center" jc="center" py="$8" gap="$3">
                                            <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
                                            <Text ta="center" color="$red10" fontSize="$4">
                                                {t('home.failedToLoad', { defaultValue: 'Failed to load services. Please try again.' })}
                                            </Text>
                                            <Pressable
                                                onPress={handleRetry}
                                                style={({ pressed }) => ({
                                                    backgroundColor: '#4A90E2',
                                                    paddingHorizontal: 20,
                                                    paddingVertical: 10,
                                                    borderRadius: 8,
                                                    transform: pressed ? [{ scale: 0.95 }] : [{ scale: 1 }]
                                                })}
                                            >
                                                <Text color="white" fontWeight="600">{t('home.retry', { defaultValue: 'Retry' })}</Text>
                                            </Pressable>
                                        </YStack>
                                    ) : filteredServices.length === 0 ? (
                                        <YStack ai="center" jc="center" py="$8">
                                            <Ionicons name="cube-outline" size={48} color="#CCCCCC" />
                                            <Text mt="$3" color="$gray10">
                                                {t('home.noServicesAvailable', { defaultValue: 'No services available' })}
                                            </Text>
                                        </YStack>
                                    ) : (
                                        <XStack fw="wrap" jc="center" gap="$3">
                                        {filteredServices.map(({ key, label, icon, color, route }) => (
                                            <Pressable
                                              key={key}
                                              onPress={() => handleServicePress(key, route)}
                                              style={({ pressed, hovered }) => ({
                                                  width: '48%',
                                                  transform: pressed ? [{ scale: 0.95 }] : hovered ? [{ scale: 1.02 }] : [{ scale: 1 }]
                                              })}
                                            >
                                              <Card
                                                pointerEvents="none"     // 👈 Card no longer blocks the tap
                                                p="$3"
                                                br="$4"
                                                bw="$0.5"
                                                boc="$gray5"
                                                ai="center"
                                                animation="quick"
                                                backgroundColor="#8F3DD2"
                                                hoverStyle={{ scale: 0.97 }}
                                                pressStyle={{ scale: 0.94 }}
                                              >
                                                <Ionicons name={icon as any} size={32} color={color} />
                                                <Spacer size="$2" />
                                                <H4 ta="center" color="white">
                                                  {t(`services.${key}`, { defaultValue: label })}
                                                </H4>
                                              </Card>
                                            </Pressable>
                                        ))}
                                        </XStack>
                                    )}
                                </>
                            )}
                        </ScrollView>
            </YStack>

            {/* AI Chat Modal */}
            <Modal
              visible={isAIChatModalVisible}
              animationType="slide"
              presentationStyle="fullScreen"
              onRequestClose={() => setIsAIChatModalVisible(false)}
            >
              <AIChatGUI onClose={() => setIsAIChatModalVisible(false)} />
            </Modal>
        </>
    );
};