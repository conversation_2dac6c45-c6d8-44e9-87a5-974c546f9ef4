import { Tabs } from 'expo-router'
import { Ionicons } from '@expo/vector-icons'
import { View } from 'tamagui'
import { Pressable } from 'react-native'
import { useTranslation } from 'react-i18next'

export default function SupplierTabsLayout() {
  const { t } = useTranslation();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#7529B3',
        tabBarStyle: {
          backgroundColor: '#fff',
          height: 60,
          elevation: 5
      },
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          headerShown: false,
          title: t('navigation.home', { defaultValue: 'Home' }),
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused? "home" : "home-outline"} size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="products"
        options={{
          headerShown: false,
          title: t('navigation.products', { defaultValue: 'Products' }),
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused? "pricetags" : "pricetags-outline"} size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
              name="profile"
              options={{
                headerShown: false,
                title: t('navigation.profile', { defaultValue: 'Profile' }),
                tabBarIcon: ({ color, size, focused }) => (
                  <Ionicons name={focused? "person" : "person-outline"} size={size} color={color} />
                ),
              }}
      />
    </Tabs>
  )
}
