import { useState } from 'react'
import { ScrollView } from 'react-native'
import {
  YStack,
  Text,
  Button,
  XStack
} from 'tamagui'
import { MotiView } from 'moti';

// Optional components
import { ImageGallery } from './ImageGallery'
import { ColorSwatchRow } from './ColorSwatchRow'
import { QtySelector } from './QtySelector'
import { Section } from './Section';
import { useCartStore } from '../CartStore';

// Optional cart logic
// import { cart } from '~/contexts/CartContext'

type Addition = { id: string; name: string; price: number }

type ClothingProductDetailsProps = {
    product: {
            id: string;
            name: string;
            image: string;
            price: number;
            category: string;
            restaurantOptions?: {
                additions?: Addition[]
                without?: string[]
                sides?: Addition[]
                /* options can be changed for each meal - resturant put sections it want */ 
            }
            clothingOptions?: {
                sizes: string[]
                colors: string[]
                gallery: string[]
            }
    };
    supplierId: string;
    supplierName: string;
}

export function ClothingProductDetails({ product, supplierId, supplierName } : ClothingProductDetailsProps) {
  const [selectedSize, setSize]   = useState(product.clothingOptions?.sizes[0]);
  const [selectedColor, setColor] = useState(product.clothingOptions?.colors[0]);
  const [qty, setQty] = useState(1);

  const { addItem } = useCartStore();

  const total = product.price * qty;

  const [showSuccess, setShowSuccess] = useState(false);

  return (
    <>
      {showSuccess && (
      <MotiView
        from={{ opacity: 0, translateY: 20 }}
        animate={{ opacity: 1, translateY: 0 }}
        exit={{ opacity: 0, translateY: 20 }}
        transition={{ type: 'timing', duration: 300 }}
        style={{
          position: 'absolute',
          bottom: 100,
          alignSelf: 'center',
          backgroundColor: '#4caf50',
          paddingVertical: 10,
          paddingHorizontal: 20,
          borderRadius: 10,
          zIndex: 999,
          shadowColor: '#000',
          shadowOpacity: 0.2,
          shadowRadius: 10,
        }}
      >
        <Text color="white" fontWeight="600">
          Added to cart ✅
        </Text>
      </MotiView>
      )}
      <ScrollView contentContainerStyle={{ paddingBottom: 120 }}>
        <YStack gap="$4" p="$4">
          {/* Photo gallery */}
          <ImageGallery images={product.clothingOptions?.gallery ?? [product.image]} />

          {/* Name & price */}
          <Text fontSize="$6" fontWeight="bold">{product.name}</Text>
          <Text color="$gray9">₪{product.price.toFixed(2)}</Text>

          {/* Size picker */}
          <Section title="Size">
            <XStack gap="$2" fw="wrap">
              {product.clothingOptions?.sizes.map(size => (
                <Button
                  key={size}
                  size="$2"
                  themeInverse
                  br="$8"
                  borderWidth={2}
                  bw="$1"
                  theme={size === selectedSize ? 'active' : undefined}
                  bg={size === selectedSize ? '$primary' : '$gray12'}
                  onPress={() => setSize(size)}
                >
                  <Text color={size === selectedSize ? '$white' : '$gray5'}>
                    {size}
                  </Text>
                </Button>
              ))}
            </XStack>
          </Section>

          {/* Color picker */}
          <Section title="Color">
            <ColorSwatchRow
              colors={product.clothingOptions?.colors ?? []}
              selected={selectedColor}
              onSelect={setColor}
            />
          </Section>

          <Text fontWeight="700" fontSize="$5">Quantity</Text>
          <QtySelector qty={qty} setQty={setQty} />

        </YStack>
      </ScrollView>
      <YStack position="absolute" zIndex={2} bottom={0} left={0} right={0} px="$4" pb={40}>
                  <Button
                    bg="$primary"
                    size="$5"
                    onPress={() => {
                      addItem({
                        product,
                        qty,
                        finalPrice: total,
                        supplierId: supplierId,
                        supplierName: supplierName,
                        supplierCategory: "clothings",
                        selectedSize: selectedSize,
                        selectedColor: selectedColor
                      });
                      setShowSuccess(true)
                      setTimeout(() => setShowSuccess(false), 2000)
                    }}
                    br="$6"
                    elevation="$3"
                    hoverStyle={{ bg: "$third" }}
                    pressStyle={{ bg: "$third" }}
                  >
                    <MotiView
                      from={{ opacity: 0.7, scale: 0.97 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ type: 'timing', duration: 200 }}
                    >
                      <Text color="white" fontWeight="600">
                        Add {qty} • ₪{total.toFixed(2)}
                      </Text>
                    </MotiView>
                  </Button>
      </YStack>
    </>
  )
}
