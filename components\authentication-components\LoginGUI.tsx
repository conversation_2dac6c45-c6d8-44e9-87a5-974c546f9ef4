import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ck, H4, H6, <PERSON>, Theme, Card, Separator } from 'tamagui';
import { CustomTextField } from '../CustomTextField';
import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form'
import { Button } from '~/components/Button';
import { Link, useRouter } from 'expo-router';
import LoginLogo from '../LoginLogo';
import { Alert, ScrollView, Dimensions } from 'react-native';
import { useCurrentUserData } from '../useCurrentUserData';
import { apiService } from '../../services/api';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useTranslation } from 'react-i18next';
import { useLanguageStore } from '../../stores/languageStore';
import { ModernLanguageSwitcher } from '../common/ModernLanguageSwitcher';
import { googleAuthService } from '../../services/googleAuthService';

type LoginGUIProps = {
    children?: React.ReactNode;
    methods: UseFormReturn<FieldValues, any, FieldValues>;
}

export const LoginGUI = ({ children, methods }: LoginGUIProps) => {
    const { t, ready } = useTranslation();
    const { isRTL } = useLanguageStore();
    const router = useRouter();
    const { handleSubmit, getValues } = methods;
    const { setCurrentUser, setLoading, setError } = useCurrentUserData();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isGoogleLoading, setIsGoogleLoading] = useState(false);

    // Don't render until translations are ready
    if (!ready) {
        return null;
    }

    const onSubmit = async () => {
      const { ['username or email']: email, password } = getValues();

      if (!email || !password) {
        Alert.alert(
          t('auth.loginFailed', { defaultValue: 'Login Failed' }),
          t('auth.pleaseEnterCredentials', { defaultValue: 'Please enter both email and password.' })
        );
        return;
      }

      setIsSubmitting(true);
      setLoading(true);
      setError(null);

      try {
        const response = await apiService.login({
          email: email.trim().toLowerCase(),
          password,
        });

        if (response.success && response.data) {
          setCurrentUser(response.data.user);

          // Redirect based on role
          if (response.data.user.role === 'customer') {
            router.push('/(customer-pages)/home');
          } else if (response.data.user.role === 'supplier') {
            router.push('/(supplier-pages)/home');
          }
        } else {
          Alert.alert(
            t('auth.loginFailed', { defaultValue: 'Login Failed' }),
            response.message || t('auth.invalidCredentials', { defaultValue: 'Invalid email or password.' })
          );
          setError(response.message || t('auth.loginFailed', { defaultValue: 'Login failed' }));
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : t('auth.networkError', { defaultValue: 'Network error occurred' });
        Alert.alert(t('auth.loginFailed', { defaultValue: 'Login Failed' }), errorMessage);
        setError(errorMessage);
      } finally {
        setIsSubmitting(false);
        setLoading(false);
      }
    };

    const handleGoogleSignIn = async () => {
      if (isGoogleLoading) return;

      setIsGoogleLoading(true);
      setError(null);

      try {
        const result = await googleAuthService.signInWithGoogle();

        if (result.success && result.user && result.accessToken) {
          // Send Google auth data to backend
          const response = await apiService.googleAuth(result.accessToken, result.user);

          if (response.success && response.data) {
            setCurrentUser(response.data.user);

            // Redirect based on role
            if (response.data.user.role === 'customer') {
              router.push('/(customer-pages)/home');
            } else if (response.data.user.role === 'supplier') {
              router.push('/(supplier-pages)/home');
            }
          } else {
            Alert.alert(
              t('auth.loginFailed', { defaultValue: 'Login Failed' }),
              response.message || t('auth.googleAuthFailed', { defaultValue: 'Google authentication failed.' })
            );
            setError(response.message || t('auth.googleAuthFailed', { defaultValue: 'Google authentication failed' }));
          }
        } else {
          if (result.error && !result.error.includes('cancel')) {
            Alert.alert(
              t('auth.loginFailed', { defaultValue: 'Login Failed' }),
              result.error || t('auth.googleAuthFailed', { defaultValue: 'Google authentication failed.' })
            );
            setError(result.error || t('auth.googleAuthFailed', { defaultValue: 'Google authentication failed' }));
          }
        }
      } catch (error) {
        console.error('Google Sign-In Error:', error);
        const errorMessage = error instanceof Error ? error.message : t('auth.googleAuthFailed', { defaultValue: 'Google authentication failed' });
        Alert.alert(
          t('auth.loginFailed', { defaultValue: 'Login Failed' }),
          errorMessage
        );
        setError(errorMessage);
      } finally {
        setIsGoogleLoading(false);
      }
    };

    const { height, width } = Dimensions.get('window');

    return (
        <Theme name="light">
            <FormProvider {...methods}>
                <YStack height={height} backgroundColor="$background">
                    {/* Header Section */}
                    <YStack
                        padding="$6"
                        paddingTop="$8"
                        backgroundColor="$primary"
                        borderBottomLeftRadius="$12"
                        borderBottomRightRadius="$12"
                        alignItems="center"
                        gap="$3"
                        position="relative"
                    >
                        {/* Language Switcher */}
                        <XStack position="absolute" top="$6" right="$6" zIndex={1000}>
                            <ModernLanguageSwitcher variant="compact" />
                        </XStack>

                        <LoginLogo />
                        <H4 color="white" fontWeight="bold" textAlign="center">
                            {t('auth.welcomeBack', { defaultValue: 'Welcome Back!' })}
                        </H4>
                        <Text color="white" fontSize="$4" textAlign="center" opacity={0.9}>
                            {t('auth.signInToContinue', { defaultValue: 'Sign in to continue your journey' })}
                        </Text>
                    </YStack>

                    {/* Content Section */}
                    <YStack flex={1}>
                        <ScrollView
                            contentContainerStyle={{
                                flexGrow: 1,
                                padding: 24,
                                paddingBottom: 120
                            }}
                            showsVerticalScrollIndicator={false}
                        >
                            <MotiView
                                from={{ opacity: 0, translateY: 50 }}
                                animate={{ opacity: 1, translateY: 0 }}
                                transition={{ type: 'timing', duration: 500 }}
                            >
                                <Card
                                    padding="$6"
                                    borderRadius="$6"
                                    backgroundColor="white"
                                    shadowColor="$shadowColor"
                                    shadowOffset={{ width: 0, height: 4 }}
                                    shadowOpacity={0.1}
                                    shadowRadius={8}
                                    elevation={5}
                                    marginTop="$4"
                                >
                                    <YStack gap="$5">
                                        <YStack gap="$2" alignItems="center">
                                            <Ionicons name="log-in" size={32} color="#7529B3" />
                                            <Text fontSize="$5" fontWeight="600" color="$gray11" textAlign="center">
                                                {t('auth.signInToAccount', { defaultValue: 'Sign In to Your Account' })}
                                            </Text>
                                        </YStack>

                                        <YStack gap="$4">
                                            <CustomTextField
                                                name="username or email"
                                                icon="person"
                                                label={t('auth.usernameOrEmail', { defaultValue: 'Username or Email' })}
                                                placeholder={t('auth.enterUsernameOrEmail', { defaultValue: 'Enter your username or email' })}
                                                keyboardType="email-address"
                                                autoCapitalize="none"
                                                required
                                            />

                                            <CustomTextField
                                                name="password"
                                                icon="lock-closed"
                                                label={t('auth.password', { defaultValue: 'Password' })}
                                                secureTextEntry
                                                placeholder={t('auth.enterPassword', { defaultValue: 'Enter your password' })}
                                                required
                                            />
                                        </YStack>

                                        <Link
                                            href="/authentication/verify-before-reset1"
                                            asChild
                                        >
                                            <Text
                                                color="$primary"
                                                fontSize="$4"
                                                fontWeight="500"
                                                textAlign="center"
                                                textDecorationLine="underline"
                                                padding="$2"
                                            >
                                                {t('auth.forgotPassword', { defaultValue: 'Forgot Password? Click Here' })}
                                            </Text>
                                        </Link>
                                    </YStack>
                                </Card>
                            </MotiView>
                        </ScrollView>
                    </YStack>

                    {/* Bottom Buttons */}
                    <YStack
                        backgroundColor="white"
                        padding="$4"
                        borderTopWidth={1}
                        borderTopColor="$borderColor"
                        shadowColor="$shadowColor"
                        shadowOffset={{ width: 0, height: -2 }}
                        shadowOpacity={0.1}
                        shadowRadius={4}
                        elevation={5}
                    >
                        <XStack gap="$3" justifyContent="space-between">
                            <Button
                                flex={1}
                                backgroundColor="$primary"
                                color="white"
                                onPress={handleSubmit(onSubmit)}
                                disabled={isSubmitting}
                                icon={
                                    isSubmitting ? (
                                        <Ionicons name="hourglass" size={20} color="white" />
                                    ) : (
                                        <Ionicons name="log-in" size={20} color="white" />
                                    )
                                }
                                fontSize="$5"
                                fontWeight="600"
                                height={56}
                            >
                                {isSubmitting ? t('auth.signingIn', { defaultValue: 'Signing In...' }) : t('auth.login', { defaultValue: 'Sign In' })}
                            </Button>

                            <Link href={{ pathname: '/authentication/signup', params: { name: 'Dan' } }} asChild>
                                <Button
                                    flex={1}
                                    backgroundColor="$secondary"
                                    hoverStyle={{ bg: '$secondary_strong' }}
                                    pressStyle={{ bg: '$secondary_strong' }}
                                    color="white"
                                    icon={<Ionicons name="person-add" size={20} color="white" />}
                                    fontSize="$5"
                                    fontWeight="600"
                                    height={56}
                                >
                                    {t('auth.signup', { defaultValue: 'Sign Up' })}
                                </Button>
                            </Link>
                        </XStack>

                        <Text
                            fontSize="$3"
                            color="$gray9"
                            textAlign="center"
                            marginTop="$3"
                        >
                            {t('auth.noAccount', { defaultValue: "Don't have an account? Create one now!" })}
                        </Text>

                        {/* Divider */}
                        <XStack alignItems="center" marginTop="$4" marginBottom="$3">
                            <Separator flex={1} />
                            <Text fontSize="$3" color="$gray9" paddingHorizontal="$3">
                                {t('auth.orContinueWith', { defaultValue: 'Or continue with' })}
                            </Text>
                            <Separator flex={1} />
                        </XStack>

                        {/* Google Sign-In Button */}
                        <Button
                            backgroundColor="white"
                            borderColor="$gray6"
                            borderWidth={1}
                            color="$gray12"
                            onPress={handleGoogleSignIn}
                            disabled={isGoogleLoading || isSubmitting}
                            icon={
                                isGoogleLoading ? (
                                    <Ionicons name="hourglass" size={20} color="#666" />
                                ) : (
                                    <Ionicons name="logo-google" size={20} color="#4285F4" />
                                )
                            }
                            fontSize="$4"
                            fontWeight="600"
                            height={48}
                            hoverStyle={{ backgroundColor: '$gray2' }}
                            pressStyle={{ backgroundColor: '$gray3' }}
                        >
                            {isGoogleLoading
                                ? t('auth.signingInWithGoogle', { defaultValue: 'Signing in with Google...' })
                                : t('auth.continueWithGoogle', { defaultValue: 'Continue with Google' })
                            }
                        </Button>
                    </YStack>

                    {children}
                </YStack>
            </FormProvider>
        </Theme>
    );
};