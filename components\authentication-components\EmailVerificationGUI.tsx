import React, { useState, useRef } from 'react';
import { YStack, XStack, H4, Text, Theme, Input } from 'tamagui';
import { ScrollView, Alert, TextInput } from 'react-native';
import { Button } from '~/components/Button';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useRouter } from 'expo-router';
import { apiService } from '../../services/api';

interface EmailVerificationGUIProps {
  email: string;
  onVerificationSuccess?: () => void;
}

export const EmailVerificationGUI: React.FC<EmailVerificationGUIProps> = ({ 
  email, 
  onVerificationSuccess 
}) => {
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const router = useRouter();
  
  // Refs for input focus management
  const inputRefs = useRef<(TextInput | null)[]>([]);

  const handleCodeChange = (value: string, index: number) => {
    if (value.length > 1) return; // Prevent multiple characters
    
    const newCode = [...verificationCode];
    newCode[index] = value;
    setVerificationCode(newCode);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !verificationCode[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyCode = async () => {
    const code = verificationCode.join('');
    console.log('Verifying code:', code);

    if (code.length !== 6) {
      Alert.alert('Invalid Code', 'Please enter the complete 6-digit verification code.');
      return;
    }

    setIsVerifying(true);

    try {
      console.log('Calling verifyEmail API...');
      const response = await apiService.verifyEmail({ token: code });
      console.log('Verification response:', response);

      if (response.success) {
        console.log('Verification successful, navigating to login...');
        Alert.alert(
          'Email Verified!',
          'Your email has been successfully verified. You can now sign in to your account.',
          [
            {
              text: 'Sign In',
              onPress: () => {
                console.log('Navigating to login page...');
                onVerificationSuccess?.();
                router.push('/authentication/login');
              }
            }
          ]
        );
      } else {
        console.log('Verification failed:', response.message);
        Alert.alert('Verification Failed', response.message || 'Invalid verification code. Please try again.');
      }
    } catch (error) {
      console.error('Verification error:', error);
      Alert.alert('Error', 'Network error. Please check your connection and try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendCode = async () => {
    setIsResending(true);

    try {
      const response = await apiService.resendVerificationEmail({ email });
      
      if (response.success) {
        Alert.alert('Code Sent', 'A new verification code has been sent to your email.');
        setVerificationCode(['', '', '', '', '', '']); // Clear current code
        inputRefs.current[0]?.focus(); // Focus first input
      } else {
        Alert.alert('Failed to Send', response.message || 'Failed to send verification code. Please try again.');
      }
    } catch (error) {
      Alert.alert('Error', 'Network error. Please check your connection and try again.');
    } finally {
      setIsResending(false);
    }
  };

  const isCodeComplete = verificationCode.every(digit => digit !== '');

  return (
    <Theme name="light">
      <YStack flex={1} backgroundColor="$background">
        <ScrollView showsVerticalScrollIndicator={false}>
          <YStack padding="$4" paddingTop="$8" gap="$6">
            {/* Header */}
            <MotiView
              from={{ opacity: 0, translateY: -20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 600 }}
            >
              <YStack alignItems="center" gap="$4">
                <YStack 
                  width={80} 
                  height={80} 
                  backgroundColor="$primary" 
                  borderRadius="$10" 
                  alignItems="center" 
                  justifyContent="center"
                >
                  <Ionicons name="mail" size={40} color="white" />
                </YStack>
                
                <YStack alignItems="center" gap="$2">
                  <H4 color="$gray12" fontWeight="bold" textAlign="center">
                    Verify Your Email
                  </H4>
                  <Text fontSize="$4" color="$gray10" textAlign="center" lineHeight="$1">
                    We've sent a 6-digit verification code to
                  </Text>
                  <Text fontSize="$4" color="$primary" fontWeight="600" textAlign="center">
                    {email}
                  </Text>
                </YStack>
              </YStack>
            </MotiView>

            {/* Verification Code Input */}
            <MotiView
              from={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'timing', duration: 600, delay: 200 }}
            >
              <YStack gap="$4" alignItems="center">
                <Text fontSize="$3" color="$gray9" textAlign="center">
                  Enter the verification code below
                </Text>
                
                <XStack gap="$3" justifyContent="center">
                  {verificationCode.map((digit, index) => (
                    <Input
                      key={index}
                      ref={(ref) => (inputRefs.current[index] = ref)}
                      width={50}
                      height={60}
                      textAlign="center"
                      fontSize="$6"
                      fontWeight="bold"
                      maxLength={1}
                      value={digit}
                      onChangeText={(value) => handleCodeChange(value, index)}
                      onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                      keyboardType="numeric"
                      borderColor={digit ? "$primary" : "$borderColor"}
                      borderWidth={2}
                      borderRadius="$4"
                      backgroundColor="white"
                      shadowColor="$shadowColor"
                      shadowOffset={{ width: 0, height: 2 }}
                      shadowOpacity={0.1}
                      shadowRadius={4}
                      elevation={2}
                    />
                  ))}
                </XStack>
              </YStack>
            </MotiView>

            {/* Action Buttons */}
            <MotiView
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 600, delay: 400 }}
            >
              <YStack gap="$3" alignItems="center">
                <Button
                  title={isVerifying ? 'Verifying...' : 'Verify'}
                  backgroundColor={isCodeComplete ? "$primary" : "$gray5"}
                  color={isCodeComplete ? "white" : "$gray8"}
                  disabled={!isCodeComplete || isVerifying}
                  onPress={handleVerifyCode}
                  alignSelf="center"
                  width="100%"
                  icon={
                    isVerifying ? (
                      <Ionicons name="hourglass" size={20} color="white" />
                    ) : (
                      <Ionicons name="checkmark-circle" size={20} color="white" />
                    )
                  }
                />

                <XStack alignItems="center" justifyContent="center" gap="$2">
                  <Text fontSize="$3" color="$gray9">
                    Didn't receive the code?
                  </Text>
                  <Button
                    title={isResending ? 'Resending...' : 'Resend'}
                    variant="ghost"
                    size="$3"
                    color="$primary"
                    backgroundColor="$blue2"
                    borderColor="$primary"
                    borderWidth={1}
                    disabled={isResending}
                    onPress={handleResendCode}
                    paddingHorizontal="$3"
                    paddingVertical="$2"
                  />
                </XStack>
              </YStack>
            </MotiView>

            {/* Help Text */}
            <MotiView
              from={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ type: 'timing', duration: 600, delay: 600 }}
            >
              <YStack gap="$2" padding="$4" backgroundColor="$blue2" borderRadius="$4">
                <Text fontSize="$3" color="$blue11" fontWeight="600">
                  💡 Tips:
                </Text>
                <Text fontSize="$2" color="$blue10" lineHeight="$1">
                  • Check your spam/junk folder if you don't see the email
                </Text>
                <Text fontSize="$2" color="$blue10" lineHeight="$1">
                  • The verification code expires in 24 hours
                </Text>
                <Text fontSize="$2" color="$blue10" lineHeight="$1">
                  • Make sure you entered the correct email address
                </Text>
              </YStack>
            </MotiView>
          </YStack>
        </ScrollView>
      </YStack>
    </Theme>
  );
};
