import { useEffect, useState } from 'react';
import { Platform, ScrollView, Dimensions, Linking, TouchableWithoutFeedback } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { View, Text, Button, YStack, XStack } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useSendPackageStore } from './useSendPackageStore';
import { useRequestPickupStore } from './useRequestPickupStore';
// Use react-native-web-maps directly for web
import MapView, { <PERSON><PERSON>, <PERSON>yline } from 'react-native-web-maps';

export default function PackageTracking() {
  const { type } = useLocalSearchParams();
  const router = useRouter();
  const { width, height } = Dimensions.get('window');
  
  const sendPackageData = useSendPackageStore((state) => state.data);
  const requestPickupData = useRequestPickupStore((state) => state.data);
  
  const [driverLocation, setDriverLocation] = useState<[number, number]>([35.9106, 31.9539]);
  const [estimatedTime, setEstimatedTime] = useState(15);

  // Get the relevant data based on type
  const trackingData = type === 'send-package' ? sendPackageData : requestPickupData;
  
  useEffect(() => {
    // Simulate driver movement
    const interval = setInterval(() => {
      setDriverLocation(prev => [
        prev[0] + (Math.random() - 0.5) * 0.001,
        prev[1] + (Math.random() - 0.5) * 0.001
      ]);
      setEstimatedTime(prev => Math.max(1, prev - 1));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const handleCallDriver = () => {
    if (Platform.OS === 'web') {
      window.open('tel:+970599123456');
    } else {
      Linking.openURL('tel:+970599123456');
    }
  };

  const getMapRegion = () => {
    if (!trackingData?.pickupLocation) {
      return {
        latitude: 31.9539,
        longitude: 35.9106,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
    }

    const pickup = trackingData.pickupLocation.coordinates;
    return {
      latitude: pickup[1],
      longitude: pickup[0],
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
  };

  const getPolylineCoordinates = () => {
    if (!trackingData?.pickupLocation) return [];
    
    const pickup = trackingData.pickupLocation.coordinates;
    const delivery = trackingData.deliveryLocation?.coordinates || pickup;
    
    return [
      { latitude: pickup[1], longitude: pickup[0] },
      { latitude: driverLocation[1], longitude: driverLocation[0] },
      { latitude: delivery[1], longitude: delivery[0] },
    ];
  };

  return (
    <ScrollView style={{ flex: 1 }}>
      {/* Map Section */}
      <View style={{ height: height * 0.4 }}>
        <MapView
          style={{ width, height: height * 0.4 }}
          region={getMapRegion()}
          showsUserLocation={true}
        >
          {/* Pickup Marker */}
          {trackingData?.pickupLocation && (
            <Marker
              coordinate={{
                latitude: trackingData.pickupLocation.coordinates[1],
                longitude: trackingData.pickupLocation.coordinates[0],
              }}
              pinColor="green"
              title="Pickup Location"
            />
          )}
          
          {/* Delivery Marker */}
          {trackingData?.deliveryLocation && (
            <Marker
              coordinate={{
                latitude: trackingData.deliveryLocation.coordinates[1],
                longitude: trackingData.deliveryLocation.coordinates[0],
              }}
              pinColor="red"
              title="Delivery Location"
            />
          )}
          
          {/* Driver Marker */}
          <Marker
            coordinate={{
              latitude: driverLocation[1],
              longitude: driverLocation[0],
            }}
            pinColor="blue"
            title="Driver Location"
          />
          
          {/* Route Polyline */}
          <Polyline
            coordinates={getPolylineCoordinates()}
            strokeColor="#007AFF"
            strokeWidth={3}
          />
        </MapView>
      </View>

      {/* Tracking Info */}
      <View p="$4">
        <YStack gap="$4">
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 500 }}
          >
            <YStack gap="$2" p="$4" bg="$blue2" br="$4">
              <Text fontSize="$6" fontWeight="bold" color="$blue11">
                Package in Transit
              </Text>
              <Text fontSize="$4" color="$blue10">
                Estimated arrival: {estimatedTime} minutes
              </Text>
            </YStack>
          </MotiView>

          {/* Driver Info */}
          <YStack gap="$3" p="$4" bg="white" br="$4" bw={1} bc="$gray5">
            <Text fontSize="$5" fontWeight="600">Driver Information</Text>
            <XStack gap="$3" alignItems="center">
              <View w={50} h={50} br={25} bg="$gray5" />
              <YStack flex={1}>
                <Text fontSize="$4" fontWeight="500">Ahmad Hassan</Text>
                <Text fontSize="$3" color="$gray10">★ 4.8 • Toyota Corolla</Text>
              </YStack>
              <Button
                size="$3"
                bg="$green9"
                color="white"
                onPress={handleCallDriver}
                icon={<Ionicons name="call" size={16} color="white" />}
              >
                Call
              </Button>
            </XStack>
          </YStack>

          {/* Package Details */}
          {trackingData && (
            <YStack gap="$3" p="$4" bg="white" br="$4" bw={1} bc="$gray5">
              <Text fontSize="$5" fontWeight="600">Package Details</Text>
              <YStack gap="$2">
                <XStack justifyContent="space-between">
                  <Text color="$gray10">Type:</Text>
                  <Text fontWeight="500">{trackingData.packageType || 'Standard'}</Text>
                </XStack>
                <XStack justifyContent="space-between">
                  <Text color="$gray10">Size:</Text>
                  <Text fontWeight="500">{trackingData.packageSize || 'Medium'}</Text>
                </XStack>
                <XStack justifyContent="space-between">
                  <Text color="$gray10">Weight:</Text>
                  <Text fontWeight="500">{trackingData.packageWeight || '2kg'}</Text>
                </XStack>
              </YStack>
            </YStack>
          )}

          <Button
            size="$4"
            variant="outlined"
            onPress={() => router.back()}
          >
            Back to Home
          </Button>
        </YStack>
      </View>
    </ScrollView>
  );
}
