// Polyfill for import.meta to fix "Cannot use 'import.meta' outside a module" error
if (typeof globalThis !== 'undefined' && !globalThis.import) {
  globalThis.import = {
    meta: {
      url: typeof window !== 'undefined' ? window.location.href : 'file:///',
      env: typeof process !== 'undefined' ? process.env : {},
    }
  };
}

// Also add to window for browser compatibility
if (typeof window !== 'undefined' && !window.import) {
  window.import = {
    meta: {
      url: window.location.href,
      env: {},
    }
  };
}

module.exports = {};
