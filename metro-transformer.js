const upstreamTransformer = require('@expo/metro-config/babel-transformer');

module.exports.transform = function ({ src, filename, options }) {
  // Add import.meta polyfill for web platform
  if (options.platform === 'web') {
    const polyfill = `
// Polyfill for import.meta to fix web compatibility issues
if (typeof globalThis !== 'undefined' && typeof globalThis.import === 'undefined') {
  globalThis.import = {
    meta: {
      url: typeof window !== 'undefined' ? window.location.href : 'file:///',
      env: typeof process !== 'undefined' ? process.env : {},
    }
  };
}

`;
    src = polyfill + src;
  }

  return upstreamTransformer.transform({ src, filename, options });
};
