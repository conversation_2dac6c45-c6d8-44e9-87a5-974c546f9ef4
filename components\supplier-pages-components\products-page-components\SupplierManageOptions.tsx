// import { useEffect, useState } from 'react';
// import { Dimensions } from 'react-native';
// import { Ionicons } from '@expo/vector-icons';
// import {
//   ScrollView,
//   YStack,
//   XStack,
//   Input,
//   Text,
//   Button,
//   Separator,
//   Card,
// } from 'tamagui';
// import { useSupplierProducts } from './useSupplierProducts';
// import { useLocalSearchParams, useRouter } from 'expo-router';
// import { v4 as uuid } from 'uuid';
// import { MotiView } from 'moti';

// type CustomOption = {
//   id: string;
//   title: string;
//   type: 'text' | 'number' | 'select' | 'multi-select';
//   values?: string[];
// };

// export default function SupplierManageOptions() {
//   const { id: productId } = useLocalSearchParams<{ id: string }>();
//   const { products, updateProduct } = useSupplierProducts();
//   const product = products.find((p) => p.id === productId);
//   const router = useRouter();
//   const windowWidth = Dimensions.get('window').width;

//   const [input, setInput] = useState({
//     name: '',
//     price: '',
//     value: '',
//     customTitle: '',
//     customValue: '',
//   });
//   const [customOptions, setCustomOptions] = useState<CustomOption[]>([]);

//   const isRestaurant = !!product?.restaurantOptions;
//   const isClothing = !!product?.clothingOptions;

//   useEffect(() => {
//     setInput({
//       name: '',
//       price: '',
//       value: '',
//       customTitle: '',
//       customValue: '',
//     });

//     const mappedOptions = (product?.customOptions || []).map((opt) => ({
//       id: opt.id,
//       title: opt.title,
//       type: opt.type,
//       values: opt.values ?? [],
//     }));

//     setCustomOptions(mappedOptions);
//   }, [productId]);

//   const saveCustomOptions = (updated: CustomOption[]) => {
//     setCustomOptions(updated);
//     updateProduct(productId, { customOptions: updated });
//   };

//   const handleAdd = (type: string) => {
//     if (type === 'addition' || type === 'side') {
//       if (!input.name || !input.price) return;
//       const newItem = { id: uuid(), name: input.name, price: parseFloat(input.price) };
//       updateProduct(productId, {
//         restaurantOptions: {
//           ...product?.restaurantOptions,
//           [type === 'addition' ? 'additions' : 'sides']: [
//             ...(product?.restaurantOptions?.[type === 'addition' ? 'additions' : 'sides'] || []),
//             newItem,
//           ],
//         },
//       });
//     } else if (type === 'without') {
//       if (!input.value) return;
//       updateProduct(productId, {
//         restaurantOptions: {
//           ...product?.restaurantOptions,
//           without: [...(product?.restaurantOptions?.without || []), input.value],
//         },
//       });
//     } else if (['sizes', 'colors', 'gallery'].includes(type)) {
//       if (!input.value) return;
//       updateProduct(productId, {
//         clothingOptions: {
//            sizes: type === 'sizes'
//             ? [...(product?.clothingOptions?.sizes || []), input.value]
//             : product?.clothingOptions?.sizes || [],
//           colors: type === 'colors'
//             ? [...(product?.clothingOptions?.colors || []), input.value]
//             : product?.clothingOptions?.colors || [],
//           gallery: type === 'gallery'
//             ? [...(product?.clothingOptions?.gallery || []), input.value]
//             : product?.clothingOptions?.gallery || [],
//         },
//       });
//     }

//     setInput({ ...input, name: '', price: '', value: '' });
//   };

//   const handleDelete = (type: string, idOrValue: string) => {
//     if (type === 'addition' || type === 'side') {
//       const updated = (product?.restaurantOptions?.[type === 'addition' ? 'additions' : 'sides'] || []).filter(
//         (a) => a.id !== idOrValue
//       );
//       updateProduct(productId, {
//         restaurantOptions: {
//           ...product?.restaurantOptions,
//           [type]: updated,
//         },
//       });
//     } else if (['without', 'sizes', 'colors', 'gallery'].includes(type)) {
//       const target = isRestaurant && type === 'without'
//         ? product?.restaurantOptions?.without
//         : product?.clothingOptions?.[type as 'sizes' | 'colors' | 'gallery'];
//       const updated = target?.filter((v) => v !== idOrValue) || [];

//       if (isRestaurant && type === 'without') {
//         updateProduct(productId, {
//           restaurantOptions: {
//             ...product?.restaurantOptions,
//             without: updated,
//           },
//         });
//       } else {
//         updateProduct(productId, {
//           clothingOptions: {
//             sizes: type === 'sizes' ? (updated || []) : (product?.clothingOptions?.sizes || []),
//             colors: type === 'colors' ? (updated || []) : (product?.clothingOptions?.colors || []),
//             gallery: type === 'gallery' ? (updated || []) : (product?.clothingOptions?.gallery || []),
//           },
//         });
//       }
//     }
//   };

//   const renderOptionSection = (label: string, type: string, list: any[]) => (
//     <Card elevate p="$4" mb="$4" br="$8" borderWidth={1} borderColor="$gray4">
//       <Text color="$primary" fontWeight="600" fontSize="$5" mb="$2">{label}</Text>

//       {(type === 'addition' || type === 'side') ? (
//         <XStack gap="$2" ai="center" mb="$2">
//           <Input placeholder="Name" flex={1} value={input.name} onChangeText={(text) => setInput({ ...input, name: text })} />
//           <Input placeholder="Price" keyboardType="decimal-pad" width={80} value={input.price} onChangeText={(text) => setInput({ ...input, price: text })} />
//         </XStack>
//       ) : (
//         <Input placeholder={`Enter ${label.toLowerCase()}`} mb="$2" value={input.value} onChangeText={(text) => setInput({ ...input, value: text })} />
//       )}

//       <Button icon={<Ionicons name="add" size={18} />} onPress={() => handleAdd(type)} bg="$secondary" hoverStyle={{ bg: '$secondary_strong' }} pressStyle={{ bg: '$secondary_strong' }} color="white">Add</Button>

//       {list.length > 0 && (
//         <YStack mt="$4" gap="$2">
//           {list.map((item: any) => (
//             <Card key={item.id || item} p="$3" br="$6" borderWidth={1} borderColor="$gray5" bg="$gray1">
//               <XStack jc="space-between" ai="center">
//                 <Text fontWeight="bold">{item.name || item} {item.price !== undefined && ` - ${item.price} ₪`}</Text>
//                 <Button chromeless icon={<Ionicons name="trash-outline" size={20} color="#f44" />} onPress={() => handleDelete(type, item.id || item)} />
//               </XStack>
//             </Card>
//           ))}
//         </YStack>
//       )}
//     </Card>
//   );

//   const renderCustomOptionEditor = () => (
//     <Card elevate p="$4" mb="$4" br="$8" borderWidth={1} borderColor="$gray4">
//       <Text color="$primary" fontWeight="600" fontSize="$5" mb="$2">Custom Option</Text>

//       <Input placeholder="Title (e.g. Spiciness)" mb="$2" value={input.customTitle} onChangeText={(text) => setInput({ ...input, customTitle: text })} />
//       <Input placeholder="Value (e.g. Mild)" mb="$2" value={input.customValue} onChangeText={(text) => setInput({ ...input, customValue: text })} />

//       <Button
//         icon={<Ionicons name="add-circle-outline" size={18} />}
//         onPress={() => {
//           if (!input.customTitle || !input.customValue) return;
//           const existing = customOptions.find((opt) => opt.title === input.customTitle);
//           const updated: CustomOption[] = existing
//           ? customOptions.map((opt) =>
//               opt.title === input.customTitle
//                 ? { ...opt, values: [...(opt.values ?? []), input.customValue] }
//                 : opt
//             )
//           : [
//               ...customOptions,
//               {
//                 id: uuid(),
//                 title: input.customTitle,
//                 type: 'select', // this must match CustomOption.type
//                 values: [input.customValue],
//               } as CustomOption, // <-- this cast is important
//             ];
//           saveCustomOptions(updated);
//           setInput({ ...input, customValue: '' });
//         }}
//         bg="$primary"
//         hoverStyle={{ bg: '$third' }}
//         pressStyle={{ bg: '$third' }}
//         color="white"
//       >
//         Add to Custom Options
//       </Button>

//       <YStack mt="$4" gap="$3">
//         {customOptions.map((opt) => (
//           <Card key={opt.id} p="$3" br="$6" borderWidth={1} borderColor="$gray5">
//             <Text fontWeight="bold" mb="$1">{opt.title} ({opt.type})</Text>
//             {(opt.values || []).map((val) => (
//               <XStack key={val} jc="space-between" ai="center" mb="$1">
//                 <Text>{val}</Text>
//                 <Button
//                   chromeless
//                   icon={<Ionicons name="close-circle" size={18} color="#f44" />}
//                   onPress={() => {
//                     const updatedValues = (opt.values || []).filter((v) => v !== val);
//                     const updated = updatedValues.length
//                       ? customOptions.map((o) => o.id === opt.id ? { ...o, values: updatedValues } : o)
//                       : customOptions.filter((o) => o.id !== opt.id);
//                     saveCustomOptions(updated);
//                   }}
//                 />
//               </XStack>
//             ))}
//           </Card>
//         ))}
//       </YStack>
//     </Card>
//   );

//   return (
//     <ScrollView p="$4" width={windowWidth} contentContainerStyle={{ paddingBottom: 120 }}>
//       <MotiView
//         from={{ opacity: 0, translateY: -20 }}
//         animate={{ opacity: 1, translateY: 0 }}
//         transition={{ duration: 500 }}
//         style={{ backgroundColor: '#7529B3', padding: 20, borderRadius: 20, marginBottom: 24 }}
//       >
//         <Text color="white" fontSize="$8" fontWeight="bold">Manage Options</Text>
//         <Text color="white" mt="$1">Add and manage your product customization options</Text>
//       </MotiView>

//       {isRestaurant && (
//         <>
//           {renderOptionSection('Additions', 'addition', product?.restaurantOptions?.additions || [])}
//           {renderOptionSection('Without', 'without', product?.restaurantOptions?.without || [])}
//           {renderOptionSection('Sides', 'side', product?.restaurantOptions?.sides || [])}
//         </>
//       )}

//       {isClothing && (
//         <>
//           {renderOptionSection('Sizes', 'sizes', product?.clothingOptions?.sizes || [])}
//           {renderOptionSection('Colors', 'colors', product?.clothingOptions?.colors || [])}
//           {renderOptionSection('Gallery URLs', 'gallery', product?.clothingOptions?.gallery || [])}
//         </>
//       )}

//       {renderCustomOptionEditor()}

//       <Separator />
//       <Button
//         mt="$6"
//         br="$10"
//         bg="$secondary"
//         color="white"
//         hoverStyle={{ bg: '$secondary_strong' }}
//         pressStyle={{ bg: '$secondary_strong' }}
//         icon={<Ionicons name="arrow-back-outline" size={20} />}
//         onPress={() => router.back()}
//       >
//         Back to Product
//       </Button>
//     </ScrollView>
//   );
// }
